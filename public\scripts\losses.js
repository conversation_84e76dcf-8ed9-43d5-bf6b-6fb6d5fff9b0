import { loadAndPopulateTransformerInfo } from './common_module.js';
import { collectFormData, fillFormWithData, setupApiFormPersistence } from './api_persistence.js';

// NOVA FUNÇÃO PARA ORGANIZAR O LAYOUT DOS CARDS PRINCIPAIS
function organizeMainCardsLayout() {
    // A função aguarda um instante para garantir que o DOM principal foi renderizado.
    // Isso é uma abordagem para modificar o layout via JS, como solicitado, 
    // embora o ideal seja ajustar isso no arquivo HTML principal.
    setTimeout(() => {
        try {
            // Seleciona os elementos 'card-body' e sobe no DOM para encontrar o container da coluna.
            // A busca é genérica para cobrir classes como col-lg-4, col-md-6, col-4, etc.
            const nominaisCardContainer = document.getElementById('condicoes-nominais-card-body')?.closest('div[class*="col"]');
            const statusGeralCardContainer = document.getElementById('status-geral-card-body')?.closest('div[class*="col"]');
            const legendaCardContainer = document.getElementById('legenda-status-card-body')?.closest('div[class*="col"]');
            
            // Define as classes antigas a serem removidas e as novas a serem adicionadas.
            const classesToRemove = ['col-lg-4', 'col-md-4', 'col-sm-12', 'col-12', 'col-3', 'col-4', 'col-5', 'col-6', 'col-2'];

            if (nominaisCardContainer) {
                nominaisCardContainer.classList.remove(...classesToRemove);
                // Diminui a largura do card 'Condições Nominais'
                nominaisCardContainer.classList.add('col-lg-2', 'col-md-12');
            } else {
                console.warn("[losses layout] Container do card 'Condições Nominais' não encontrado.");
            }

            if (statusGeralCardContainer) {
                statusGeralCardContainer.classList.remove(...classesToRemove);
                // Aumenta a largura do card 'Status Geral'
                statusGeralCardContainer.classList.add('col-lg-5', 'col-md-6');
            } else {
                 console.warn("[losses layout] Container do card 'Status Geral' não encontrado.");
            }

            if (legendaCardContainer) {
                legendaCardContainer.classList.remove(...classesToRemove);
                // Aumenta a largura do card 'Legenda e Limites'
                legendaCardContainer.classList.add('col-lg-5', 'col-md-6');
            } else {
                 console.warn("[losses layout] Container do card 'Legenda e Limites' não encontrado.");
            }

        } catch (error) {
            console.error("Erro ao organizar o layout dos cards:", error);
        }
    }, 100); // Um pequeno delay para segurança.
}


let cachedBasicData = null;
let cachedLoadResults = null;

async function waitForApiSystem() {
    return new Promise((resolve) => {
        let attempts = 0;
        const maxAttempts = 50; 
        const check = () => {
            if (window.apiDataSystem) {
                resolve();
            } else if (attempts < maxAttempts) {
                attempts++;
                setTimeout(check, 100);
            } else {
                console.warn('[losses] Timeout waiting for apiDataSystem.');
                resolve(); 
            }
        };
        check();
    });
}


const formatVal = (val, precision = 2, unit = '') => { if (val === null || val === undefined || (typeof val === 'number' && isNaN(val))) return '-'; const num = Number(val); if (isNaN(num)) return typeof val === 'string' && val.trim() !== "" ? val : '-'; return num.toFixed(precision) + unit; };
function getStatusClass(statusString) { if (!statusString) return ''; if (statusString.includes('CRÍTICO') || statusString.includes('EPS V≤ > Limite') || statusString.includes('EPS V> > Limite') || statusString.includes('EPS > Limite') || statusString.includes('P_DUT > Limite')) return 'status-critical-red'; if (statusString.includes('ALERTA') || statusString.includes('Vteste V> > Limite') || statusString.includes('Vteste > Vmax Banco') || statusString.includes('G1 insuf.')) return 'status-alert-orange'; if (statusString.includes('OK')) return 'status-ok-green'; return ''; }
function getStatusWithIcon(statusString) { if (!statusString) return 'N/A'; if (statusString.includes('CRÍTICO') || statusString.includes('EPS V≤ > Limite') || statusString.includes('EPS V> > Limite') || statusString.includes('EPS > Limite') || statusString.includes('P_DUT > Limite')) return `🚨 ${statusString}`; if (statusString.includes('ALERTA') || statusString.includes('Vteste V> > Limite') || statusString.includes('Vteste > Vmax Banco') || statusString.includes('G1 insuf.')) return `⚠️ ${statusString}`; if (statusString.includes('OK')) return `✅ ${statusString}`; if (statusString.includes('Potência Subcompensada') || statusString.includes('Subcompensada')) return `ℹ️ ${statusString}`; return statusString; }
function getSutEpsClass(current_eps_a, eps_limit_pos, eps_limit_neg) { if (current_eps_a === null || current_eps_a === undefined || isNaN(current_eps_a)) return 'sut-eps-na'; if (current_eps_a > eps_limit_pos + 1e-6 || current_eps_a < eps_limit_neg - 1e-6) return 'sut-eps-critical'; if (current_eps_a < -1e-6) return 'sut-eps-excessive'; const percent_from_pos_limit = (current_eps_a / eps_limit_pos) * 100; if (percent_from_pos_limit < 50) return 'sut-eps-normal'; if (percent_from_pos_limit < 85) return 'sut-eps-alert'; return 'sut-eps-high'; }
function getStatusIcon(current_eps_a, eps_limit_pos, eps_limit_neg) { if (current_eps_a === null || current_eps_a === undefined || isNaN(current_eps_a)) return '<i class="fas fa-question-circle text-muted ms-1" title="N/A"></i>'; if (current_eps_a > eps_limit_pos + 1e-6 || current_eps_a < eps_limit_neg - 1e-6) return '<i class="fas fa-times-circle text-danger ms-1" title="Crítico (Fora dos limites EPS)"></i>'; if (current_eps_a < -1e-6) return '<i class="fas fa-arrow-alt-circle-down text-info ms-1" title="Corrente Capacitiva"></i>'; const percent_from_pos_limit = (current_eps_a / eps_limit_pos) * 100; if (percent_from_pos_limit < 50) return '<i class="fas fa-check-circle text-success ms-1" title="Normal"></i>'; if (percent_from_pos_limit < 85) return '<i class="fas fa-exclamation-circle text-warning ms-1" title="Alerta"></i>'; return '<i class="fas fa-exclamation-triangle text-orange ms-1" title="Alto"></i>'; }
function isQEfetivaExceedingPteste(qEfetivaMvar, pTesteMva) { if (qEfetivaMvar === null || qEfetivaMvar === undefined || typeof qEfetivaMvar !== 'number' || isNaN(qEfetivaMvar) || qEfetivaMvar <= 0) return false; if (pTesteMva === null || pTesteMva === undefined || typeof pTesteMva !== 'number' || isNaN(pTesteMva) || pTesteMva <= 0) return false; return qEfetivaMvar > pTesteMva + 0.001; }
function isVtestExceedingBankLimit(vtestKv, bankVoltageKv, factorOvervoltage) { if (!vtestKv || !bankVoltageKv || !factorOvervoltage) return false; const limitKv = bankVoltageKv * factorOvervoltage; return vtestKv > limitKv + 0.001; }
function isItestExceedingCtLimit(itestA, ctLimitA = 2000.0) { if (!itestA || !ctLimitA) return false; return itestA > ctLimitA + 0.001; }

// Função helper para verificar se corrente excede limite CT e aplicar formatação
function formatCurrentWithCtValidation(currentA, ctLimitA = 2000.0) {
    if (!currentA || isNaN(currentA)) return formatVal(currentA, 1);

    const exceedsLimit = isItestExceedingCtLimit(currentA, ctLimitA);
    const formattedValue = formatVal(currentA, 1);

    if (exceedsLimit) {
        return `<span class="text-danger fw-bold" title="🚫 CRÍTICO: Corrente (${formattedValue}A) excede limite CT (${ctLimitA}A)">${formattedValue} 🚨</span>`;
    }

    return formattedValue;
}
function isVtestExceedingSutLimit(vtestKv, sutMaxKv = 140.0) { if (!vtestKv || !sutMaxKv) return false; return vtestKv > sutMaxKv + 0.001; }
function isPatvExceedingDutLimit(patvKw, dutLimitKw = 1350.0) { if (!patvKw || !dutLimitKw) return false; return patvKw > dutLimitKw + 0.001; }


// Function to determine the MAX overall capacitor bank voltage from limitsInfo
function getMaxOverallCapBankVoltage(limitsInfo) {
    let maxVoltage = 0;
    if (limitsInfo?.capacitor_power_limits_by_voltage) {
        const voltageKeys = Object.keys(limitsInfo.capacitor_power_limits_by_voltage);
        if (voltageKeys.length > 0) {
            maxVoltage = Math.max(...voltageKeys.map(k => parseFloat(k)).filter(v => !isNaN(v) && v > 0));
        }
    }
    return maxVoltage;
}

// Helper to get parameters for a single scenario based on current radio selections
function getScenarioParametersForStatusUpdate(tapIndexGlobal, scenIndexOriginal, cenario, limitsInfo, basicData, factorOvervoltage) {
    const { test_params_cenario } = cenario;
    const vTestKv = test_params_cenario.tensao_kv;
    const iTestA = test_params_cenario.corrente_a;
    const pativaKw = test_params_cenario.pativa_kw;
    const qTesteMvar = test_params_cenario.q_teste_mvar;
    const sqrt3_factor = (basicData.tipo_transformador.toLowerCase() === 'trifásico') ? Math.sqrt(3) : 1.0;

    const sfRadio = document.querySelector(`input[name="bank-option-sf-${tapIndexGlobal}-${scenIndexOriginal}"]:checked`);
    const cfRadio = document.querySelector(`input[name="bank-option-cf-${tapIndexGlobal}-${scenIndexOriginal}"]:checked`);
    const sfConfigIndex = sfRadio ? parseInt(sfRadio.dataset.configIndex) : -1;
    const cfConfigIndex = cfRadio ? parseInt(cfRadio.dataset.configIndex) : -1;

    const sfAvailableConfigs = cenario.cap_bank_sf?.available_configurations || [];
    const cfAvailableConfigs = cenario.cap_bank_cf?.available_configurations || [];
    const fallbackConfig = { q_efetiva_banco_mvar: 0.0, meets_ideal_power_req: false, eps_within_limits_estimation: false, eps_within_limits: false };

    const activeSfConfig = (sfConfigIndex !== -1 && sfAvailableConfigs.length > sfConfigIndex) ? sfAvailableConfigs[sfConfigIndex] : fallbackConfig;
    const activeCfConfig = (cfConfigIndex !== -1 && cfAvailableConfigs.length > cfConfigIndex) ? cfAvailableConfigs[cfConfigIndex] : fallbackConfig;
    
    const currentQEffSf = activeSfConfig.q_efetiva_banco_mvar;
    const currentQEffCf = activeCfConfig.q_efetiva_banco_mvar;

    const cfBankIsEffectivelyAvailable = cfAvailableConfigs.length > 0 && !(cfAvailableConfigs.length === 1 && cfAvailableConfigs[0].q_config && cfAvailableConfigs[0].q_config.startsWith("N/A"));
    
    let isEpsSfOkActualForIdealSut = activeSfConfig.eps_within_limits_estimation || activeSfConfig.eps_within_limits;
    let isEpsCfOkActualForIdealSut = cfBankIsEffectivelyAvailable ? (activeCfConfig.eps_within_limits_estimation || activeCfConfig.eps_within_limits) : true;

    let idealSutTapKvForThisScenario = null;
    if (cenario.sut_eps_analysis && Array.isArray(cenario.sut_eps_analysis) && cenario.sut_eps_analysis.length > 0) {
        const idealEntry = cenario.sut_eps_analysis.find(sutEntry => sutEntry.is_ideal_tap === true);
        if (idealEntry && typeof idealEntry.sut_tap_kv === 'number') {
            idealSutTapKvForThisScenario = idealEntry.sut_tap_kv;
        } else { 
            const targetSutRefV = vTestKv * 1000.0;
            let localSutTaps = Array.from(new Set(cenario.sut_eps_analysis.map(s => s.sut_tap_kv).filter(kv => kv !== undefined && typeof kv === 'number'))).sort((a,b) => a - b);
            if(localSutTaps.length > 0) {
                const tapsGteVtest = localSutTaps.filter(tapKv => (tapKv*1000) >= targetSutRefV - 1e-6);
                if(tapsGteVtest.length > 0) idealSutTapKvForThisScenario = Math.min(...tapsGteVtest);
                else idealSutTapKvForThisScenario = Math.max(...localSutTaps);
            }
        }
    }


    if (idealSutTapKvForThisScenario !== null && vTestKv > 0 && limitsInfo.eps_current_limit_negative_a !== undefined) {
        const SUT_BT_VOLTAGE = 480; // Should be from constants
        const ratioSut = (SUT_BT_VOLTAGE > 1e-6) ? (idealSutTapKvForThisScenario * 1000) / SUT_BT_VOLTAGE : 0;
        
        const iCapSfForIdealSUT = (vTestKv * sqrt3_factor > 1e-6 && currentQEffSf > 1e-6) ? (currentQEffSf * 1000) / (vTestKv * sqrt3_factor) : 0;
        let sfCurrentIdeal = (iTestA - iCapSfForIdealSUT) * ratioSut;
        // Removido fator √3 para monofásico conforme solicitado
        // if (basicData.tipo_transformador.toLowerCase() === 'monofásico') sfCurrentIdeal *= Math.sqrt(3);
        isEpsSfOkActualForIdealSut = (sfCurrentIdeal >= limitsInfo.eps_current_limit_negative_a - 1e-6 && sfCurrentIdeal <= limitsInfo.eps_current_limit_positive_a + 1e-6);

        if (cfBankIsEffectivelyAvailable) {
            const iCapCfForIdealSUT = (vTestKv * sqrt3_factor > 1e-6 && currentQEffCf > 1e-6) ? (currentQEffCf * 1000) / (vTestKv * sqrt3_factor) : 0;
            let cfCurrentIdeal = (iTestA - iCapCfForIdealSUT) * ratioSut;
            // Removido fator √3 para monofásico conforme solicitado
            // if (basicData.tipo_transformador.toLowerCase() === 'monofásico') cfCurrentIdeal *= Math.sqrt(3);
            isEpsCfOkActualForIdealSut = (cfCurrentIdeal >= limitsInfo.eps_current_limit_negative_a - 1e-6 && cfCurrentIdeal <= limitsInfo.eps_current_limit_positive_a + 1e-6);
        } else {
            isEpsCfOkActualForIdealSut = true;
        }
    }

    return {
        testVoltageKv: vTestKv,
        iTestA: iTestA,
        qTesteMvar: qTesteMvar,
        activePowerKw: pativaKw,
        factorCapBancOvervoltage: factorOvervoltage,
        sfBankNominalKv: cenario.cap_bank_sf?.tensao_disp_kv,
        sfEpsOk: isEpsSfOkActualForIdealSut,
        sfPowerIdealMet: activeSfConfig.meets_ideal_power_req,
        sfAvailableConfigs: sfAvailableConfigs,
        cfBankNominalKv: cenario.cap_bank_cf?.tensao_disp_kv,
        cfEpsOk: isEpsCfOkActualForIdealSut,
        cfPowerIdealMet: activeCfConfig.meets_ideal_power_req,
        cfAvailableConfigs: cfAvailableConfigs,
        cfActive: cfBankIsEffectivelyAvailable,
        limitsInfo: limitsInfo
    };
}

// Replicates Python's get_detailed_scenario_status
function getDetailedScenarioStatusJS(params) {
    const {
        testVoltageKv, iTestA, qTesteMvar, activePowerKw, factorCapBancOvervoltage,
        sfBankNominalKv, sfEpsOk, sfPowerIdealMet, sfAvailableConfigs,
        cfBankNominalKv, cfEpsOk, cfPowerIdealMet, cfAvailableConfigs, cfActive,
        limitsInfo
    } = params;

    const epsilon = 1e-6;
    let status_v_menor_parts = [];
    let status_v_maior_parts = [];
    let status_global_parts = [];
    let cap_required_mvar_v_menor = null;
    let cap_required_mvar_v_maior = null;

    // --- V≤ (SF Bank) Logic ---
    if (sfBankNominalKv && qTesteMvar > epsilon && testVoltageKv > epsilon) {
        const qBancoNecessariaSf = qTesteMvar * Math.pow(sfBankNominalKv / testVoltageKv, 2);
        cap_required_mvar_v_menor = qBancoNecessariaSf;
        const voltageLimitsSf = limitsInfo?.capacitor_power_limits_by_voltage?.[sfBankNominalKv.toString()];
        if (voltageLimitsSf) {
            const grupo1MaxSf = voltageLimitsSf.grupo1?.max;
            const grupo1_2MaxSf = voltageLimitsSf.grupo1_2?.max;
            if (grupo1_2MaxSf !== undefined && qBancoNecessariaSf > grupo1_2MaxSf + epsilon) {
                status_v_menor_parts.push(`Q_req V≤ CRÍTICO (${qBancoNecessariaSf.toFixed(1)} > ${grupo1_2MaxSf.toFixed(1)} MVAr)`);
            } else if (grupo1MaxSf !== undefined && qBancoNecessariaSf > grupo1MaxSf + epsilon) {
                 status_v_menor_parts.push(`G1 insuf. V≤, req G1+G2 (${qBancoNecessariaSf.toFixed(1)} MVAr)`);
            }
        }
    }
    
    const vMenorHasEpsOkConfig = sfEpsOk; // This now directly reflects the selected config's EPS status at ideal SUT
    const vMaiorHasEpsOkConfigForVMenorCheck = cfActive ? cfEpsOk : true; // If CF not active, assume it's "OK" for this check
    
    if (!vMenorHasEpsOkConfig && !vMaiorHasEpsOkConfigForVMenorCheck) {
        status_v_menor_parts.push("EPS V≤ > Limite");
    } else if (!sfPowerIdealMet && qTesteMvar > epsilon) {
        status_v_menor_parts.push("Subcompensada V≤");
    }

    // --- V> (CF Bank) Logic ---
    if (cfActive) {
        if (cfBankNominalKv && qTesteMvar > epsilon && testVoltageKv > epsilon) {
            const qBancoNecessariaCf = qTesteMvar * Math.pow(cfBankNominalKv / testVoltageKv, 2);
            cap_required_mvar_v_maior = qBancoNecessariaCf;
            const voltageLimitsCf = limitsInfo?.capacitor_power_limits_by_voltage?.[cfBankNominalKv.toString()];
            if (voltageLimitsCf) {
                const grupo1MaxCf = voltageLimitsCf.grupo1?.max;
                const grupo1_2MaxCf = voltageLimitsCf.grupo1_2?.max;
                if (grupo1_2MaxCf !== undefined && qBancoNecessariaCf > grupo1_2MaxCf + epsilon) {
                    status_v_maior_parts.push(`Q_req V> CRÍTICO (${qBancoNecessariaCf.toFixed(1)} > ${grupo1_2MaxCf.toFixed(1)} MVAr)`);
                } else if (grupo1MaxCf !== undefined && qBancoNecessariaCf > grupo1MaxCf + epsilon) {
                    status_v_maior_parts.push(`G1 insuf. V>, req G1+G2 (${qBancoNecessariaCf.toFixed(1)} MVAr)`);
                }
            }
        }


        
        const vMaiorHasEpsOkConfigLocal = cfEpsOk; // Reflects selected CF config's EPS at ideal SUT
        const vMenorHasEpsOkConfigForVMaiorCheck = sfEpsOk; // Reflects selected SF config's EPS at ideal SUT

        if (!vMaiorHasEpsOkConfigLocal && !vMenorHasEpsOkConfigForVMaiorCheck) {
            status_v_maior_parts.push("EPS V> > Limite");
        } else if (!cfPowerIdealMet && qTesteMvar > epsilon) {
            status_v_maior_parts.push("Subcompensada V>");
        }
    }

    // --- Global Logic ---
    const maxOverallCapVoltage = getMaxOverallCapBankVoltage(limitsInfo);
    if (maxOverallCapVoltage > 0) {
        // Apply overvoltage factor to the maximum bank voltage for global validation
        const maxOverallCapVoltageWithFactor = maxOverallCapVoltage * factorCapBancOvervoltage;
        if (testVoltageKv > maxOverallCapVoltageWithFactor + epsilon) {
            status_global_parts.push(`Vteste > Vmax Banco (${maxOverallCapVoltage.toFixed(1)}kV)`);
        }
    }
    const dutPowerLimit = limitsInfo?.dut_power_limit_kw || 1350.0;
    if (activePowerKw > dutPowerLimit + epsilon) {
        status_global_parts.push(`P_DUT > Limite (${dutPowerLimit.toFixed(0)}kW)`);
    }

    // CT Current Limit validation
    const ctCurrentLimit = limitsInfo?.ct_current_limit_a || 2000.0;
    if (iTestA > ctCurrentLimit + epsilon) {
        status_global_parts.push(`🚫 TESTE IMPOSSÍVEL: Itest > CT (${ctCurrentLimit.toFixed(0)}A)`);
    }

    // SUT Voltage Limit validation
    const sutMaxVoltage = limitsInfo?.sut_at_max_voltage_kv || 140.0;
    if (testVoltageKv > sutMaxVoltage + epsilon) {
        status_global_parts.push(`🚫 TESTE IMPOSSÍVEL: Vtest > SUT (${sutMaxVoltage.toFixed(0)}kV)`);
    }
    
    const cap_required_max = Math.max(cap_required_mvar_v_menor || 0, cap_required_mvar_v_maior || 0);
    // Add Q_required to global if it's a critical issue not covered by V</V> parts.
    // However, Q_req issues are typically part of V</V> specific messages.
    // The Python code adds "Potência necessária: X MVAr" to global if cap_required_max > 0.
    // This can be verbose. Let's include it if there's a Q_req CRITICAL message in V< or V>
    if (status_v_menor_parts.some(s => s.includes("Q_req V≤ CRÍTICO")) || (cfActive && status_v_maior_parts.some(s => s.includes("Q_req V> CRÍTICO")))) {
        if (cap_required_max > epsilon && !status_global_parts.some(s => s.includes("Potência necessária"))) { // Avoid double-printing if logic changes
             // status_global_parts.push(`Potência necessária: ${cap_required_max.toFixed(1)} MVAr`); // Optional, can be verbose
        }
    }
    
    return {
        status_v_menor: status_v_menor_parts.length > 0 ? status_v_menor_parts.join(" | ") : "OK",
        status_v_maior: cfActive ? (status_v_maior_parts.length > 0 ? status_v_maior_parts.join(" | ") : "OK") : "N/A",
        status_global: status_global_parts.length > 0 ? status_global_parts.join(" | ") : "OK",
        cap_required_mvar: cap_required_max > epsilon ? cap_required_max : null
    };
}


window.losses_module = window.losses_module || {};
window.losses_module.collectBankSelections = collectBankSelections;
window.losses_module.restoreBankSelections = restoreBankSelections;
window.losses_module.handleBankOptionChange = (event, tapIndexGlobal, scenIndexOriginal) => {
    if (!cachedLoadResults || !cachedBasicData || !cachedLoadResults.limitsInfo) {
        console.error("[handleBankOptionChange] Dados críticos não cacheados."); return;
    }

    const { cenariosDetalhadosPorTap, limitsInfo } = cachedLoadResults;
    const tapData = cenariosDetalhadosPorTap[tapIndexGlobal];
    const cenario = tapData.cenarios_do_tap[scenIndexOriginal];
    
    // Update Qef and Icap cells (as in your existing code, ensure this uses newly selected values)
    const sfRadioChecked = document.querySelector(`input[name="bank-option-sf-${tapIndexGlobal}-${scenIndexOriginal}"]:checked`);
    const cfRadioChecked = document.querySelector(`input[name="bank-option-cf-${tapIndexGlobal}-${scenIndexOriginal}"]:checked`);
    const sfConfigIndex = sfRadioChecked ? parseInt(sfRadioChecked.dataset.configIndex) : -1;
    const cfConfigIndex = cfRadioChecked ? parseInt(cfRadioChecked.dataset.configIndex) : -1;
    const sfAvailableConfigs = cenario.cap_bank_sf?.available_configurations || [];
    const cfAvailableConfigs = cenario.cap_bank_cf?.available_configurations || [];
    const fallbackConfig = { q_efetiva_banco_mvar: 0.0, meets_ideal_power_req: false, eps_within_limits_estimation: false, eps_within_limits: false };
    const activeSfConfig = (sfConfigIndex !== -1 && sfAvailableConfigs.length > sfConfigIndex) ? sfAvailableConfigs[sfConfigIndex] : fallbackConfig;
    const activeCfConfig = (cfConfigIndex !== -1 && cfAvailableConfigs.length > cfConfigIndex) ? cfAvailableConfigs[cfConfigIndex] : fallbackConfig;
    const currentQEffSf = activeSfConfig.q_efetiva_banco_mvar;
    const currentQEffCf = activeCfConfig.q_efetiva_banco_mvar;
    const vTestKv = cenario.test_params_cenario.tensao_kv;
    const sqrt3_factor = (cachedBasicData.tipo_transformador.toLowerCase() === 'trifásico') ? Math.sqrt(3) : 1.0;

    const iCapSfNew = (vTestKv * sqrt3_factor > 1e-6 && currentQEffSf > 1e-6) ? (currentQEffSf * 1000) / (vTestKv * sqrt3_factor) : 0;
    const qefSfCell = document.getElementById(`qef-sf-${tapIndexGlobal}-${scenIndexOriginal}`);
    const icapSfCell = document.getElementById(`icap-sf-${tapIndexGlobal}-${scenIndexOriginal}`);
    if (qefSfCell) qefSfCell.innerHTML = formatVal(currentQEffSf, 3);
    if (icapSfCell) icapSfCell.innerHTML = formatVal(iCapSfNew, 2);
    // ... (qEfetivaSfExceedsPteste toggle and title)
    const qEfetivaSfExceedsPteste = isQEfetivaExceedingPteste(currentQEffSf, cenario.test_params_cenario.pteste_mva);
    if (qefSfCell) {
        qefSfCell.classList.toggle('q-efetiva-excede', qEfetivaSfExceedsPteste);
        qefSfCell.title = qEfetivaSfExceedsPteste ? `Q_efetiva_banco V≤ (${formatVal(currentQEffSf,3)} MVAr) excede P_teste (${formatVal(cenario.test_params_cenario.pteste_mva, 3)} MVAr)!` : '';
    }

    const cfBankIsEffectivelyAvailable = cfAvailableConfigs.length > 0 && !(cfAvailableConfigs.length === 1 && cfAvailableConfigs[0].q_config && cfAvailableConfigs[0].q_config.startsWith("N/A"));
    let iCapCfNew = 0;
    if (cfBankIsEffectivelyAvailable) {
       iCapCfNew = (vTestKv * sqrt3_factor > 1e-6 && currentQEffCf > 1e-6) ? (currentQEffCf * 1000) / (vTestKv * sqrt3_factor) : 0;
    }
    const qefCfCell = document.getElementById(`qef-cf-${tapIndexGlobal}-${scenIndexOriginal}`);
    const icapCfCell = document.getElementById(`icap-cf-${tapIndexGlobal}-${scenIndexOriginal}`);
    if (qefCfCell) qefCfCell.innerHTML = formatVal(currentQEffCf, 3);
    if (icapCfCell) icapCfCell.innerHTML = formatVal(iCapCfNew, 2);
    // ... (qEfetivaCfExceedsPteste toggle and title)
    const qEfetivaCfExceedsPteste = isQEfetivaExceedingPteste(currentQEffCf, cenario.test_params_cenario.pteste_mva);
     if (qefCfCell) {
        qefCfCell.classList.toggle('q-efetiva-excede', qEfetivaCfExceedsPteste);
        qefCfCell.title = qEfetivaCfExceedsPteste ? `Q_efetiva_banco V> (${formatVal(currentQEffCf,3)} MVAr) excede P_teste (${formatVal(cenario.test_params_cenario.pteste_mva, 3)} MVAr)!` : '';
    }

    // Get parameters for detailed status calculation for this specific scenario
    const factorOvervoltage = parseFloat(document.getElementById('factor-cap-banc-overvoltage').value) || 1.1;
    const scenarioStatusParams = getScenarioParametersForStatusUpdate(
        tapIndexGlobal, scenIndexOriginal, cenario, limitsInfo, cachedBasicData, factorOvervoltage
    );
    const detailedStatus = getDetailedScenarioStatusJS(scenarioStatusParams);

    // Update the V≤ and V> cells in the table row
    const scenTestName = cenario.nome_cenario_teste;
    let targetTableForStatusUpdate = null;
    document.querySelectorAll('#resultados-perdas-carga .card').forEach(card => {
        const header = card.querySelector('.card-header');
        if (header && header.textContent.toUpperCase().includes(`PERDAS EM CARGA ${scenTestName.toUpperCase()}`)) {
            targetTableForStatusUpdate = card.querySelector('table.losses-main-table');
        }
    });

    if (targetTableForStatusUpdate) {
        const rows = targetTableForStatusUpdate.querySelectorAll('tbody tr');
        if (rows[tapIndexGlobal]) { // tapIndexGlobal should map to the visual row for this DUT tap
            const statusCells = rows[tapIndexGlobal].querySelectorAll('td.col-status');
            if (statusCells.length >= 2) {
                statusCells[0].className = `col-status ${getStatusClass(detailedStatus.status_v_menor)}`;
                statusCells[0].innerHTML = getStatusWithIcon(detailedStatus.status_v_menor);
                statusCells[1].className = `col-status ${getStatusClass(detailedStatus.status_v_maior)}`;
                statusCells[1].innerHTML = getStatusWithIcon(detailedStatus.status_v_maior);
            }
        }
    }
    
    const sutEpsTableContainer = document.getElementById(`sut-eps-table-container-${scenTestName.replace(/\s+/g, '-')}`); // Ensure ID matches generator
    if (sutEpsTableContainer) {
        sutEpsTableContainer.innerHTML = generateHorizontalSutEpsTable(
            cenariosDetalhadosPorTap, scenTestName, limitsInfo
        );
    }
    
    initializeBankTooltips();
    updateStatusGeralDynamically();
    // Salva automaticamente as seleções quando um radiobutton é alterado
    saveBankSelectionsToStore();
};


function updateStatusGeralDynamically() {
    if (!cachedLoadResults || !cachedLoadResults.cenariosDetalhadosPorTap || !cachedLoadResults.limitsInfo || !cachedBasicData) {
        console.warn("[updateStatusGeralDynamically] Missing cached data to update general status.");
        const statusGeralContainerFallback = document.getElementById('status-geral-card-body');
        if (statusGeralContainerFallback) statusGeralContainerFallback.innerHTML = '<p class="text-muted">Aguardando cálculo ou dados completos.</p>';
        return; 
    }

    const statusGeralContainer = document.getElementById('status-geral-card-body');
    if (!statusGeralContainer) return;

    const { cenariosDetalhadosPorTap, limitsInfo } = cachedLoadResults;
    const factorOvervoltage = parseFloat(document.getElementById('factor-cap-banc-overvoltage')?.value) || 1.1;

    let issues = {
        P_DUT_LimiteExceeded: false,
        Vtest_Vmax_BancoExceeded: false,
        EPS_Limite_Excedido: false,
        CT_Current_Exceeded: false,
        SUT_Voltage_Exceeded: false,
        Q_Banco_Critico: false,
        Q_Banco_Alerta_G1_Insuf: false,
        max_Q_req_MVAr_crit: 0,
        max_Q_req_MVAr_alerta: 0,
        scenariosWithPDUTIssue: [],
        scenariosWithVmaxIssue: [],
        scenariosWithEPSIssue: [],
        scenariosWithCTIssue: [],
        scenariosWithSUTIssue: [],
        scenariosWithQCritico: [],
        scenariosWithQAlerta: []
    };

    cenariosDetalhadosPorTap.forEach((tapData, tapIndexGlobal) => {
        tapData.cenarios_do_tap.forEach((cenario, scenIndexOriginal) => {
            const scenarioId = `${tapData.nome_tap} / ${cenario.nome_cenario_teste}`;
            // Usar os dados de status calculados pelo backend em vez de recalcular no frontend
            const detailedStatus = {
                status_global: cenario.status_global || "OK",
                status_v_menor: cenario.status_v_menor || "OK",
                status_v_maior: cenario.status_v_maior || "OK",
                cap_required_mvar: cenario.cap_required_mvar || 0
            };

            if (detailedStatus.status_global.includes("P_DUT > Limite")) {
                issues.P_DUT_LimiteExceeded = true;
                if (!issues.scenariosWithPDUTIssue.includes(scenarioId)) issues.scenariosWithPDUTIssue.push(scenarioId);
            }
            if (detailedStatus.status_global.includes("Vteste > Vmax Banco")) {
                issues.Vtest_Vmax_BancoExceeded = true;
                 if (!issues.scenariosWithVmaxIssue.includes(scenarioId)) issues.scenariosWithVmaxIssue.push(scenarioId);
            }
            if (detailedStatus.status_global.includes("Itest > CT")) {
                issues.CT_Current_Exceeded = true;
                if (!issues.scenariosWithCTIssue.includes(scenarioId)) issues.scenariosWithCTIssue.push(scenarioId);
            }
            if (detailedStatus.status_global.includes("Vtest > SUT")) {
                issues.SUT_Voltage_Exceeded = true;
                if (!issues.scenariosWithSUTIssue.includes(scenarioId)) issues.scenariosWithSUTIssue.push(scenarioId);
            }
            if (detailedStatus.status_v_menor.includes("EPS V≤ > Limite") || detailedStatus.status_v_maior.includes("EPS V> > Limite")) {
                issues.EPS_Limite_Excedido = true;
                 if (!issues.scenariosWithEPSIssue.includes(scenarioId)) issues.scenariosWithEPSIssue.push(scenarioId);
            }
            if (detailedStatus.status_v_menor.includes("Q_req V≤ CRÍTICO") || detailedStatus.status_v_maior.includes("Q_req V> CRÍTICO")) {
                issues.Q_Banco_Critico = true;
                if (detailedStatus.cap_required_mvar > issues.max_Q_req_MVAr_crit) {
                    issues.max_Q_req_MVAr_crit = detailedStatus.cap_required_mvar;
                }
                if (!issues.scenariosWithQCritico.includes(scenarioId)) issues.scenariosWithQCritico.push(scenarioId);
            } else if (detailedStatus.status_v_menor.includes("G1 insuf. V≤") || detailedStatus.status_v_maior.includes("G1 insuf. V>")) {
                issues.Q_Banco_Alerta_G1_Insuf = true;
                 if (detailedStatus.cap_required_mvar > issues.max_Q_req_MVAr_alerta) {
                    issues.max_Q_req_MVAr_alerta = detailedStatus.cap_required_mvar;
                }
                if (!issues.scenariosWithQAlerta.includes(scenarioId)) issues.scenariosWithQAlerta.push(scenarioId);
            }
        });
    });

    let statusGeralHtml = '<div>';
    let hasAnyIssue = false;

    if (issues.P_DUT_LimiteExceeded) {
        statusGeralHtml += `<p class="mb-2 text-danger"><strong>🚨 CRÍTICO - Potência DUT:</strong><br>P_DUT > Limite (${(limitsInfo.dut_power_limit_kw || 1350.0).toFixed(0)}kW) em cenário(s) como: ${issues.scenariosWithPDUTIssue.slice(0,1).join(', ')}.</p>`;
        hasAnyIssue = true;
    }
    if (issues.Vtest_Vmax_BancoExceeded) {
        const maxOverallV = getMaxOverallCapBankVoltage(limitsInfo);
        statusGeralHtml += `<p class="mb-2 text-danger"><strong>🚨 CRÍTICO - Tensão Máx. Banco:</strong><br>Vtest excede Vmax global do banco (${maxOverallV.toFixed(1)}kV) em cenário(s) como: ${issues.scenariosWithVmaxIssue.slice(0,1).join(', ')}.</p>`;
        hasAnyIssue = true;
    }
    if (issues.EPS_Limite_Excedido) {
        statusGeralHtml += `<p class="mb-2 text-danger"><strong>🚨 CRÍTICO - Limite EPS:</strong><br>Corrente EPS fora da faixa (${limitsInfo.eps_current_limit_negative_a}A a ${limitsInfo.eps_current_limit_positive_a}A) em cenário(s) como: ${issues.scenariosWithEPSIssue.slice(0,1).join(', ')}.</p>`;
        hasAnyIssue = true;
    }
    if (issues.CT_Current_Exceeded) {
        const ctLimit = limitsInfo.ct_current_limit_a || 2000.0;
        statusGeralHtml += `<p class="mb-2 text-danger"><strong>🚫 CRÍTICO - TESTE IMPOSSÍVEL:</strong><br>Corrente de teste excede limite do CT (${ctLimit.toFixed(0)}A) em cenário(s) como: ${issues.scenariosWithCTIssue.slice(0,1).join(', ')}.</p>`;
        hasAnyIssue = true;
    }
    if (issues.SUT_Voltage_Exceeded) {
        const sutMaxVoltage = limitsInfo.sut_at_max_voltage_kv || 140.0;
        statusGeralHtml += `<p class="mb-2 text-danger"><strong>🚫 CRÍTICO - TESTE IMPOSSÍVEL:</strong><br>Tensão de teste excede limite do SUT (${sutMaxVoltage.toFixed(0)}kV) em cenário(s) como: ${issues.scenariosWithSUTIssue.slice(0,1).join(', ')}.</p>`;
        hasAnyIssue = true;
    }
    if (issues.Q_Banco_Critico) {
        statusGeralHtml += `<p class="mb-2 text-danger"><strong>🚨 CRÍTICO - Q Banco:</strong><br>Potência reativa necessária (até ${issues.max_Q_req_MVAr_crit.toFixed(1)} MVAr) excede G1+G2 em cenário(s) como: ${issues.scenariosWithQCritico.slice(0,1).join(', ')}.</p>`;
        hasAnyIssue = true;
    } else if (issues.Q_Banco_Alerta_G1_Insuf) { // Only show if not already critical
        statusGeralHtml += `<p class="mb-2 text-warning"><strong>⚠️ ALERTA - Q Banco:</strong><br>Potência reativa necessária (até ${issues.max_Q_req_MVAr_alerta.toFixed(1)} MVAr) excede G1 (requer G1+G2) em cenário(s) como: ${issues.scenariosWithQAlerta.slice(0,1).join(', ')}.</p>`;
        hasAnyIssue = true;
    }

    if (!hasAnyIssue) {
        statusGeralHtml += `<p class="mb-2 text-success"><strong>✅ Status Geral:</strong><br>OK para todos os cenários com as configurações de banco selecionadas.</p>`;
    }

    const warningMsgElement = statusGeralContainer.querySelector('.status-recalc-warning');
    if(warningMsgElement) warningMsgElement.remove();
    statusGeralHtml += `<p class="mb-0 mt-2 text-info status-recalc-warning"><i class="fas fa-info-circle"></i> Status Geral reflete as <strong>seleções atuais</strong> dos bancos.</p>`;

    statusGeralHtml += '</div>';
    statusGeralContainer.innerHTML = statusGeralHtml;
}


// --- Funções de Display e Cálculo (No-Load e Load) ---

function getDynamicEpsTypeInfo(limites) {
    const epsApparentPowerLimitKva = limites?.eps_aparente_power_limit_kva || 1450.0;
    const epsCurrentLimitPosA = limites?.eps_current_limit_positive_a || 2000.0;
    const dutPowerLimitKw = limites?.dut_power_limit_kw || 1350.0;
    let sutMaxVoltageKv = limites?.sut_at_max_voltage_kv || 140.0;
    let sutMinVoltageKv = limites?.sut_at_min_voltage_kv || 14.0;

    let tipoFonteEPS = "Trifásico";
    let capacidadeEPS = "1450 kVA";
    let limiteDUT = "1350 kW";
    let potenciaSUT = "15 MVA";

    // Determinar tipo baseado na combinação de limites EPS e DUT
    if (epsCurrentLimitPosA <= 660.1 && dutPowerLimitKw <= 483.1) {
        // Monofásico: 500 kVA, ±660A, 483kW
        tipoFonteEPS = "Monofásico";
        capacidadeEPS = "500 kVA";
        limiteDUT = "483 kW";
        potenciaSUT = "5 MVA";
        // Para Monofásico, usar tensões fase-neutro (linha-linha / √3)
        sutMinVoltageKv = (sutMinVoltageKv / Math.sqrt(3)).toFixed(2);
        sutMaxVoltageKv = (sutMaxVoltageKv / Math.sqrt(3)).toFixed(2);
    } else if (epsCurrentLimitPosA <= 1050.1 && dutPowerLimitKw <= 837.1) {
        // Bifásico: 866 kVA, ±1050A, 837kW
        tipoFonteEPS = "Bifásico";
        capacidadeEPS = "866 kVA";
        limiteDUT = "837 kW";
        potenciaSUT = "10 MVA";
    } else {
        // Trifásico: 1450 kVA, ±2000A, 1350kW
        tipoFonteEPS = "Trifásico";
        capacidadeEPS = "1450 kVA";
        limiteDUT = "1350 kW";
        potenciaSUT = "15 MVA";
    }

    return {
        tipoFonteEPS,
        capacidadeEPS,
        epsCurrentLimitPosA,
        dutPowerLimitKw,
        limiteDUT,
        sutMaxVoltageKv,
        sutMinVoltageKv,
        potenciaSUT
    };
}

function populateObservationsAndLegend(tertiary_usage_info, resultados_dut_niveis_tensao, analise_sut_eps_vazio, limites, validacao_eps_power, validacao_ct_current, validacao_sut_voltage) {
    const attentionItemsWrapper = document.getElementById('no-load-attention-items-container-wrapper');
    const legendCardArea = document.getElementById('legend-card-no-load-area');

    if (!limites || !attentionItemsWrapper || !legendCardArea) {
        console.warn("[losses] Elementos de observações/legenda (vazio) não encontrados.");
        return;
    }

    const epsApparentPowerLimitKva = limites.eps_aparente_power_limit_kva || 1450.0;
    const ctCurrentLimitA = limites.ct_current_limit_a || 2000.0;

    // Obter informações dinâmicas do tipo EPS e DUT
    const { tipoFonteEPS, capacidadeEPS, epsCurrentLimitPosA, dutPowerLimitKw, sutMaxVoltageKv, sutMinVoltageKv, potenciaSUT } = getDynamicEpsTypeInfo(limites);

    let observationsText = [];

    // Validações de impedimento
    if (validacao_sut_voltage?.tem_impedimento) {
        validacao_sut_voltage.niveis_criticos.forEach(nivel => {
            const alertInfo = validacao_sut_voltage.alertas_por_nivel[nivel];
            if (alertInfo?.excede_limite_sut) {
                const sutMaxKv = alertInfo.sut_max_voltage_kv || sutMaxVoltageKv;
                const sutType = alertInfo.sut_eps_type || tipoFonteEPS;
                const sutMaxKvFormatted = (typeof sutMaxKv === 'number') ? sutMaxKv.toFixed(1) : sutMaxKv;
                observationsText.push(`🚫 TESTE IMPOSSÍVEL ${nivel.toUpperCase()}: Tensão de teste (${alertInfo.tensao_teste_kv.toFixed(1)}kV) excede limite do SUT ${sutType} (${sutMaxKvFormatted}kV)`);
            }
        });
    }

    if (validacao_ct_current?.tem_impedimento) {
        validacao_ct_current.niveis_criticos.forEach(nivel => {
            const alertInfo = validacao_ct_current.alertas_por_nivel[nivel];
            if (alertInfo?.excede_limite_ct) {
                observationsText.push(`🚫 TESTE IMPOSSÍVEL ${nivel.toUpperCase()}: Corrente de teste (${alertInfo.corrente_teste_a.toFixed(1)}A) excede limite do CT (${ctCurrentLimitA}A)`);
            }
        });
    }

    if (validacao_eps_power?.tem_impedimento) {
        validacao_eps_power.niveis_criticos.forEach(nivel => {
            const alertInfo = validacao_eps_power.alertas_por_nivel[nivel];
            if (alertInfo?.excede_limite) {
                observationsText.push(`🚨 CRÍTICO DE ENSAIO ${nivel.toUpperCase()}: Potência de ensaio (${alertInfo.potencia_ensaio_kva.toFixed(1)} kVA) excede limite EPS ${tipoFonteEPS} (${epsApparentPowerLimitKva} kVA)`);
            }
        });
    }

    // Outras validações...
    if (tertiary_usage_info) {
        const firstPuInfo = tertiary_usage_info['1.0pu'];
        if (firstPuInfo) {
            if (firstPuInfo.using_terciary) {
                observationsText.push(`${firstPuInfo.status}`);
            } else if (firstPuInfo.status.includes('CRÍTICO')) {
                observationsText.push(`🚫 TESTE IMPOSSÍVEL: ${firstPuInfo.status}`);
            }
        }
    }

    ['1.0pu', '1.1pu', '1.2pu'].forEach(pu => {
        const puKeyProjeto = `potencia_ativa_${pu.replace('.', '_')}_kw`;
        const puDisplay = pu.replace("pu", "").toUpperCase() + " PU";
        if (resultados_dut_niveis_tensao?.projeto?.[puKeyProjeto] > dutPowerLimitKw) {
            observationsText.push(`${puDisplay} (Projeto): Potência > ${dutPowerLimitKw} kW`);
        }
        if (resultados_dut_niveis_tensao?.aco_m4?.[puKeyProjeto] > dutPowerLimitKw) {
            observationsText.push(`${puDisplay} (Aço): Potência > ${dutPowerLimitKw} kW`);
        }
    });

    if (analise_sut_eps_vazio) {
        Object.entries(analise_sut_eps_vazio).forEach(([puLevel, analysis]) => {
            const puDisplay = puLevel.replace("pu", "").toUpperCase() + " PU";
            if (analysis.status !== "OK" && !analysis.status.includes("Nenhum tap SUT")) {
                observationsText.push(`${puDisplay}: ${analysis.status}`);
            } else if (analysis.taps_info?.length > 0) {
                const criticalOverPos = analysis.taps_info.some(t => t.is_over_positive);
                if (criticalOverPos) {
                    observationsText.push(`🚨 CRÍTICO ${puDisplay}: Corrente EPS > ${epsCurrentLimitPosA}A (alguns taps)`);
                }
            }
        });
    }

    // Atualizar observações
    attentionItemsWrapper.innerHTML = observationsText.length > 0 ?
        `<div class="alert alert-warning alert-sm small p-2"><ul class="mb-0 ps-3">${observationsText.map(obs => `<li>${obs}</li>`).join('')}</ul></div>` : '';

    // Atualizar legenda com informações dinâmicas
    legendCardArea.innerHTML = `
        <div class="mb-3">
            <p class="mb-2 small fw-bold">Limites DUT:</p>
            <div class="mb-2">
                <span class="badge table-danger me-1">Vermelho</span> = Potência > ${dutPowerLimitKw} kW (${tipoFonteEPS})
            </div>
        </div>
        <div class="mb-3">
            <p class="mb-2 small fw-bold">Fonte EPS/SUT Utilizada:</p>
            <div class="mb-1"><span class="badge bg-info text-white">${tipoFonteEPS}</span></div>
            <div class="small text-muted">
                • EPS: ${capacidadeEPS}, ±${epsCurrentLimitPosA}A<br>
                • SUT: ${sutMinVoltageKv}-${sutMaxVoltageKv}kV, ${potenciaSUT}
            </div>
        </div>
        <div class="mb-0">
            <p class="mb-2 small fw-bold">Status SUT/EPS:</p>
            <div class="row gx-1 gy-1 small">
                <div class="col-6"><span class="badge sut-eps-critical-bg text-dark">🚨 Fora da Faixa (±${epsCurrentLimitPosA}A)</span></div>
                <div class="col-6"><span class="badge sut-eps-high-bg text-dark">🔶 85-100% do Limite</span></div>
                <div class="col-6"><span class="badge sut-eps-alert-bg text-dark">⚠️ 50-85% do Limite</span></div>
                <div class="col-6"><span class="badge sut-eps-normal-bg text-dark">✅ < 50% do Limite</span></div>
            </div>
        </div>`;
}
function displayNoLoadResults(results) {
    const { parametros_gerais_comparativo, resultados_dut_niveis_tensao, analise_sut_eps_vazio, limites, tertiary_usage_info, validacao_eps_power, validacao_ct_current, validacao_sut_voltage } = results; const geraisContainer = document.getElementById('parametros-gerais-card-body'); if (geraisContainer && parametros_gerais_comparativo) { const fonteUsada = parametros_gerais_comparativo.fonte_tensao_usada || 'BT'; const tensaoLabel = fonteUsada === 'Terciário' ? 'Tensão Nominal Terciário (kV)' : 'Tensão Nominal BT (kV)'; const correnteLabel = fonteUsada === 'Terciário' ? 'Corrente Nominal Terciário (A)' : 'Corrente Nominal BT (A)'; const fonteIndicator = fonteUsada === 'Terciário' ? '<span class="badge bg-warning text-dark ms-2">USANDO TERCIÁRIO DO DUT</span>' : '<span class="badge bg-success text-white ms-2">USANDO BT DO DUT</span>'; geraisContainer.innerHTML = `<div class="table-responsive"><table class="table table-sm table-striped table-hover small caption-top"><caption class="small text-muted text-center table-caption-top">Comparativo Projeto vs. Aço (Calculado) ${fonteIndicator}</caption><thead><tr class="table-light"><th>Parâmetro</th><th class="text-end">Valor (Projeto)</th><th class="text-end">Valor (Aço ${document.getElementById('tipo-aco')?.value || 'M4'})</th></tr></thead><tbody><tr><td>${tensaoLabel}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.tensao_nominal_bt_kv, 1)}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.tensao_nominal_bt_kv, 1)}</td></tr><tr><td>${correnteLabel}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.corrente_nominal_bt_a, 1)}</td><td class="text-end">-</td></tr><tr><td>Frequência (Hz)</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.frequencia_hz, 0)}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.frequencia_hz, 0)}</td></tr><tr><td>Potência Mag. (kVAr)</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.potencia_mag_kvar_projeto, 2)}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.potencia_mag_kvar_aco_m4, 2)}</td></tr><tr><td>Fator de Perdas Mag. (VAr/kg)</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.fator_perdas_mag_kvar_kg_projeto, 2)}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.fator_perdas_mag_var_kg_aco_m4, 2)}</td></tr><tr><td>Fator de Perdas (W/kg)</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.fator_perdas_kw_kg_projeto, 2)}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.fator_perdas_kw_kg_aco_m4, 2)}</td></tr><tr><td>Peso do Núcleo (Ton)</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.peso_nucleo_ton_projeto, 3)}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.peso_nucleo_ton_aco_m4, 3)}</td></tr><tr><td>Corrente de Excitação (%)</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.corrente_excitacao_perc_projeto, 2)}</td><td class="text-end fw-bold">${formatVal(parametros_gerais_comparativo.corrente_excitacao_perc_aco_m4, 2)}</td></tr></tbody></table></div>`; } else if (geraisContainer) { geraisContainer.innerHTML = '<div class="text-muted text-center small p-3">Erro ao carregar parâmetros gerais.</div>'; } const dutContainer = document.getElementById('dut-voltage-level-results-body'); if (dutContainer && resultados_dut_niveis_tensao && limites) { const { projeto, aco_m4 } = resultados_dut_niveis_tensao; const dutPowerLimitKw = limites.dut_power_limit_kw; const createRow = (param, p_1_0, p_1_1, p_1_2, a_1_0, a_1_1, a_1_2, isPowerRow = false, isApparentPowerRow = false) => { const highlight = (valueStr) => { if (isPowerRow && param.includes('Potência Ativa')) { const numericValue = parseFloat(String(valueStr).match(/^-?[\d.]+/)?.[0]); if (!isNaN(numericValue) && numericValue > dutPowerLimitKw) { return 'table-danger'; } } if (isApparentPowerRow && param.includes('Potência de Ensaio')) { const numericValue = parseFloat(String(valueStr).match(/^-?[\d.]+/)?.[0]); const epsApparentPowerLimitKva = limites.eps_aparente_power_limit_kva || 1450.0; if (!isNaN(numericValue) && numericValue > epsApparentPowerLimitKva) { return 'table-danger'; } } return ''; }; const addTooltip = (valueStr, nivel, isApparentPowerRow, isAcoRow = false) => { if (isApparentPowerRow && param.includes('Potência de Ensaio')) { const numericValue = parseFloat(String(valueStr).match(/^-?[\d.]+/)?.[0]); const epsApparentPowerLimitKva = limites.eps_aparente_power_limit_kva || 1450.0; if (!isNaN(numericValue) && numericValue > epsApparentPowerLimitKva) { const tipo = isAcoRow ? 'Aço' : 'Projeto'; return `<span title="⚠️ CRÍTICO: Potência de ensaio ${tipo} ${nivel} (${numericValue} kVA) excede limite EPS (${epsApparentPowerLimitKva} kVA)" data-bs-toggle="tooltip">${valueStr} 🚨</span>`; } } return valueStr; }; return ` <tr> <td rowspan="2" class="align-middle fw-bold">${param}</td> <td>Projeto</td> <td class="text-end ${highlight(p_1_0)}">${addTooltip(p_1_0, '1.0pu', isApparentPowerRow, false)}</td> <td class="text-end ${highlight(p_1_1)}">${addTooltip(p_1_1, '1.1pu', isApparentPowerRow, false)}</td> <td class="text-end ${highlight(p_1_2)}">${addTooltip(p_1_2, '1.2pu', isApparentPowerRow, false)}</td> </tr> <tr> <td>Aço ${document.getElementById('tipo-aco')?.value || 'M4'}</td> <td class="text-end ${highlight(a_1_0)}">${addTooltip(a_1_0, '1.0pu', isApparentPowerRow, true)}</td> <td class="text-end ${highlight(a_1_1)}">${addTooltip(a_1_1, '1.1pu', isApparentPowerRow, true)}</td> <td class="text-end ${highlight(a_1_2)}">${addTooltip(a_1_2, '1.2pu', isApparentPowerRow, true)}</td> </tr>`; }; const fonteUsada = projeto.voltage_source_1_0_pu || 'BT'; const fonteIndicator = fonteUsada === 'Terciário' ? '<span class="badge bg-warning text-dark ms-2">USANDO TERCIÁRIO DO DUT</span>' : '<span class="badge bg-success text-white ms-2">USANDO BT DO DUT</span>'; dutContainer.innerHTML = `<div class="table-responsive"><table class="table table-sm table-striped table-hover small caption-top"><caption class="small text-muted text-center table-caption-top">DUT - Projeto vs. Aço (Calculado) ${fonteIndicator}</caption><thead><tr class="table-light"><th style="width: 25%;">Parâmetro</th><th style="width: 15%;" class="text-center">Origem</th><th style="width: 20%;" class="text-end">1.0 pu</th><th style="width: 20%;" class="text-end">1.1 pu</th><th style="width: 20%;" class="text-end">1.2 pu</th></tr></thead><tbody>${createRow('Tensão de Teste (kV)', formatVal(projeto.tensao_1_0_pu_kv, 1), formatVal(projeto.tensao_1_1_pu_kv, 1), formatVal(projeto.tensao_1_2_pu_kv, 1), formatVal(aco_m4.tensao_1_0_pu_kv, 1), formatVal(aco_m4.tensao_1_1_pu_kv, 1), formatVal(aco_m4.tensao_1_2_pu_kv, 1))}${createRow('Corrente de Excitação (A)',
                    `${formatCurrentWithCtValidation(projeto.corrente_1_0_pu_a)} (${formatVal(projeto.corrente_1_0_pu_perc_nominal_bt, 1)}%)`,
                    `${formatCurrentWithCtValidation(projeto.corrente_1_1_pu_a)} (${formatVal(projeto.corrente_1_1_pu_perc_nominal_bt, 1)}%)`,
                    projeto.corrente_1_2_pu_a !== null ? `${formatCurrentWithCtValidation(projeto.corrente_1_2_pu_a)} (${formatVal(projeto.corrente_1_2_pu_perc_nominal_bt, 1)}%)` : '-',
                    `${formatCurrentWithCtValidation(aco_m4.corrente_1_0_pu_a)} (${formatVal(aco_m4.corrente_1_0_pu_perc_nominal_bt, 1)}%)`,
                    `${formatCurrentWithCtValidation(aco_m4.corrente_1_1_pu_a)} (${formatVal(aco_m4.corrente_1_1_pu_perc_nominal_bt, 1)}%)`,
                    `${formatCurrentWithCtValidation(aco_m4.corrente_1_2_pu_a)} (${formatVal(aco_m4.corrente_1_2_pu_perc_nominal_bt, 1)}%)`)}${createRow('Potência de Ensaio (kVA)', formatVal(projeto.potencia_1_0_pu_kva, 0), formatVal(projeto.potencia_1_1_pu_kva, 0), formatVal(projeto.potencia_1_2_pu_kva, 0), formatVal(aco_m4.potencia_1_0_pu_kva, 0), formatVal(aco_m4.potencia_1_1_pu_kva, 0), formatVal(aco_m4.potencia_1_2_pu_kva, 0), false, true)}${createRow('Potência Ativa (kW)', formatVal(projeto.potencia_ativa_1_0_pu_kw, 1), formatVal(projeto.potencia_ativa_1_1_pu_kw, 1), formatVal(projeto.potencia_ativa_1_2_pu_kw, 1), formatVal(aco_m4.potencia_ativa_1_0_pu_kw, 1), formatVal(aco_m4.potencia_ativa_1_1_pu_kw, 1), formatVal(aco_m4.potencia_ativa_1_2_pu_kw, 1), true)}</tbody></table></div>`; } else if (dutContainer) { dutContainer.innerHTML = '<div class="text-muted text-center small p-3">Erro ao carregar resultados DUT.</div>'; } const sutContainer = document.getElementById('sut-analysis-results-area'); if (sutContainer && analise_sut_eps_vazio && limites) { const puLevels = Object.keys(analise_sut_eps_vazio).sort(); const epsCurrentLimitPosA = limites.eps_current_limit_positive_a; const epsCurrentLimitNegA = limites.eps_current_limit_negative_a; let sutHtml = '<div class="row g-2">'; puLevels.forEach(puLevel => { const analysis = analise_sut_eps_vazio[puLevel]; sutHtml += `<div class="col-xl-2 col-lg-3 col-md-4 col-sm-12"><div class="card card-sm h-100"><div class="card-header card-header-sm text-center small fw-bold card-header-dark">Análise para ${puLevel.replace("pu", "").toUpperCase()} PU</div><div class="card-body p-1 table-responsive">`; if (analysis?.status === "OK" && analysis.taps_info?.length > 0) { sutHtml += `<table class="table table-sm table-striped table-hover small mb-0"><thead><tr class="table-light"><th>Tap SUT (kV)</th><th class="text-end">I_EPS (A)</th><th class="text-end">Status</th></tr></thead><tbody>`; analysis.taps_info.forEach(tap => { const current_eps = tap.corrente_eps_a; const rowClass = getSutEpsClass(current_eps, epsCurrentLimitPosA, epsCurrentLimitNegA) + '-bg text-dark'; sutHtml += `<tr class="${rowClass}"><td>${formatVal(tap.sut_tap_kv, 1)}</td><td class="text-end">${formatVal(current_eps, 1)}</td><td class="text-end">${getStatusIcon(current_eps, epsCurrentLimitPosA, epsCurrentLimitNegA)}</td></tr>`; }); sutHtml += `</tbody></table>`; } else { sutHtml += `<p class="text-muted text-center small m-1 p-2">${analysis?.status || 'N/A'}</p>`; } sutHtml += `</div></div></div>`; }); sutHtml += `<div class="col-xl-3 col-lg-3 col-md-6 col-sm-12"><div class="card card-sm h-100"><div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">OBSERVAÇÕES IMPORTANTES</h6></div><div class="card-body p-2" id="no-load-attention-items-container-wrapper"></div></div></div><div class="col-xl-3 col-lg-3 col-md-6 col-sm-12"><div class="card card-sm h-100"><div class="card-header card-header-sm"><h6 class="text-center m-0 card-header-title-sm">LEGENDA E OBSERVAÇÕES</h6></div><div class="card-body p-2" id="legend-card-no-load-area"></div></div></div></div>`; sutContainer.innerHTML = sutHtml; } else if (sutContainer) { sutContainer.innerHTML = '<div class="text-muted text-center small p-3">Erro ao carregar análise SUT/EPS.</div>'; } setTimeout(() => { populateObservationsAndLegend(tertiary_usage_info, resultados_dut_niveis_tensao, analise_sut_eps_vazio, limites, validacao_eps_power, validacao_ct_current, validacao_sut_voltage); }, 100); initializeBankTooltips();
}

function renderBankOptionsHTML(bankType, configurations, tapIndexGlobal, scenIndexOriginal, targetVoltageKv) {
    if (!configurations || configurations.length === 0) {
        return '<span class="text-muted small">N/A (Sem Configs)</span>';
    }
    if (configurations.length === 1 && configurations[0] && configurations[0].q_config && 
        (configurations[0].q_config.startsWith('N/A') || configurations[0].q_config.includes("Falha") || configurations[0].q_config.includes("Insuf."))) {
        return `<span class="text-muted small">${configurations[0].q_config} ${configurations[0].group_info ? '('+configurations[0].group_info+')' : ''}</span>`;
    }

    let html = `<div class="bank-options-table-container">
                    <table class="table table-sm table-borderless bank-options-inner-table mb-0">
                        <thead>
                            <tr>
                                <th class="p-0 small text-muted text-center" style="width: 8%;">Sel.</th>
                                <th class="p-0 small text-muted text-center" style="width: 12%;">V(kV)</th>
                                <th class="p-0 small text-muted text-center" style="width: 12%;">CS</th>
                                <th class="p-0 small text-muted text-center" style="width: 23%;">Q</th>
                                <th class="p-0 small text-muted text-center" style="width: 20%;">Qn(MVAr)</th>
                                <th class="p-0 small text-muted text-center" style="width: 25%;">Grupo</th>
                            </tr>
                        </thead>
                        <tbody>`;

    configurations.forEach((config, configIndex) => {
        if (!config) { 
            return; 
        }
        const uniqueId = `bank-radio-${bankType}-${tapIndexGlobal}-${scenIndexOriginal}-${configIndex}`;
        // is_initial_display_default comes from Python and should be respected for the first render.
        const isInitialDisplay = config.is_initial_display_default === true; 

        const voltage = formatVal(targetVoltageKv, 1);
        const qProv = formatVal(config.q_provided_mvar, 3);
        const csIcon = config.cs_config && !config.cs_config.startsWith("N/A") ? `<span class="bank-icon" title="CS: ${config.cs_config}" data-bs-toggle="tooltip">🔌</span>` : '<span class="bank-icon-na">-</span>';
        const qConfigDisplay = config.q_config ? config.q_config.replace(/Q/g, '').replace(/^N\/A.*/, '-') : '-'; 
        const qIcon = config.q_config && !config.q_config.startsWith("N/A") && !config.q_config.includes("Falha") && !config.q_config.includes("Insuf.") ? `<span class="bank-icon" title="Q: ${config.q_config}" data-bs-toggle="tooltip">${qConfigDisplay}</span>` : '<span class="bank-icon-na">-</span>';
        const groupInfo = config.group_info || 'N/A';
        
        html += `<tr>
                    <td class="p-0 text-center align-middle">
                        <input class="form-check-input" type="radio" 
                               name="bank-option-${bankType}-${tapIndexGlobal}-${scenIndexOriginal}" 
                               id="${uniqueId}" 
                               ${isInitialDisplay ? 'checked' : ''}
                               onchange="window.losses_module.handleBankOptionChange(event, ${tapIndexGlobal}, ${scenIndexOriginal})"
                               data-bank-type="${bankType}"
                               data-config-index="${configIndex}"
                               data-is-overall-best="${config.is_default === true}"> 
                    </td>
                    <td class="p-0 text-center align-middle small">${voltage}</td>
                    <td class="p-0 text-center align-middle small">${csIcon}</td>
                    <td class="p-0 text-center align-middle small">${qIcon}</td>
                    <td class="p-0 text-center align-middle small">${qProv}</td>
                    <td class="p-0 text-center align-middle small">${groupInfo}</td>
                 </tr>`;
    });
    html += '</tbody></table></div>';
    return html;
}

function updateLoadLossesLegend(limitsInfo) {
    const legendCardArea = document.getElementById('legenda-status-card-body');
    if (!legendCardArea || !limitsInfo) return;

    // Obter informações dinâmicas do tipo EPS e DUT
    const { tipoFonteEPS, capacidadeEPS, epsCurrentLimitPosA, dutPowerLimitKw, sutMaxVoltageKv, sutMinVoltageKv, potenciaSUT } = getDynamicEpsTypeInfo(limitsInfo);

    // Gerar tabela de limites de potência dos bancos de capacitores
    const capacitorPowerLimits = limitsInfo.capacitor_power_limits_by_voltage || {};
    const sortedVoltages = Object.keys(capacitorPowerLimits)
        .filter(k => k && parseFloat(k) > 0)
        .sort((a, b) => parseFloat(a) - parseFloat(b));

    let capacitorLimitsTableHtml = '';
    if (sortedVoltages.length > 0) {
        capacitorLimitsTableHtml = `
            <div class="table-responsive">
                <table class="table table-sm table-bordered mb-1" style="font-size: 0.7rem;">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center p-1" colspan="3">Limites Banco Capacitor (MVAr)</th>
                        </tr>
                        <tr>
                            <th class="text-center p-1">Tensão (kV)</th>
                            <th class="text-center p-1">G1</th>
                            <th class="text-center p-1">G1+2</th>
                        </tr>
                    </thead>
                    <tbody>`;

        sortedVoltages.forEach(voltage_str => {
            const limits = capacitorPowerLimits[voltage_str];
            const g1Min = limits.grupo1?.min;
            const g1Max = limits.grupo1?.max;
            const g1_2Min = limits.grupo1_2?.min;
            const g1_2Max = limits.grupo1_2?.max;

            const g1Range = (g1Min !== undefined && g1Max !== undefined)
                ? `${formatVal(g1Min, 1)}-${formatVal(g1Max, 1)}`
                : '-';
            const g1_2Range = (g1_2Min !== undefined && g1_2Max !== undefined)
                ? `${formatVal(g1_2Min, 1)}-${formatVal(g1_2Max, 1)}`
                : '-';

            capacitorLimitsTableHtml += `
                <tr>
                    <td class="text-center p-1">${voltage_str}</td>
                    <td class="text-center p-1">${g1Range}</td>
                    <td class="text-center p-1">${g1_2Range}</td>
                </tr>`;
        });

        capacitorLimitsTableHtml += `
                    </tbody>
                </table>
            </div>`;
    }

    legendCardArea.innerHTML = `
        <div class="small">
            <div class="row g-2">
                <!-- Coluna 1: Limites e Status -->
                <div class="col-lg-3 col-md-6">
                    <!-- Tabela de Limites DUT -->
                    <div class="mb-2">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered mb-1" style="font-size: 0.7rem;">
                                <thead class="table-light">
                                    <tr>
                                        <th class="text-center p-1" colspan="3">Limites DUT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge table-danger" style="font-size: 0.6rem;">Vermelho</span></td>
                                        <td class="p-1">Patv > ${dutPowerLimitKw} kW</td>
                                        <td class="p-1 text-center">${tipoFonteEPS}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Tabela de Status dos Cenários -->
                    <div class="mb-2">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered mb-0" style="font-size: 0.7rem;">
                                <thead class="table-light">
                                    <tr>
                                        <th class="text-center p-1" colspan="2">Status Cenário</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge status-ok-green">✅</span></td>
                                        <td class="p-1">OK</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge status-alert-orange">⚠️</span></td>
                                        <td class="p-1">Alerta</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge status-critical-red">🚨</span></td>
                                        <td class="p-1">Crítico</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Coluna 2: Fonte EPS/SUT -->
                <div class="col-lg-3 col-md-6">
                    <div class="mb-2">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered mb-0" style="font-size: 0.7rem;">
                                <thead class="table-light">
                                    <tr>
                                        <th class="text-center p-1" colspan="3">Fonte EPS/SUT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge bg-info text-white" style="font-size: 0.6rem;">${tipoFonteEPS}</span></td>
                                        <td class="p-1">EPS: ${capacidadeEPS}</td>
                                        <td class="p-1 text-center">±${epsCurrentLimitPosA}A</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge bg-secondary text-white" style="font-size: 0.6rem;">SUT</span></td>
                                        <td class="p-1">${sutMinVoltageKv}-${sutMaxVoltageKv}kV</td>
                                        <td class="p-1 text-center">${potenciaSUT}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Coluna 3: Corrente SUT/EPS -->
                <div class="col-lg-3 col-md-6">
                    <div class="mb-2">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered mb-0" style="font-size: 0.7rem;">
                                <thead class="table-light">
                                    <tr>
                                        <th class="text-center p-1" colspan="3">Corrente SUT/EPS</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge sut-eps-excessive-bg text-dark">➖</span></td>
                                        <td class="p-1">Capacitiva</td>
                                        <td class="p-1 text-center">-</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge sut-eps-normal-bg text-dark">✅</span></td>
                                        <td class="p-1">Normal</td>
                                        <td class="p-1 text-center">&lt;50%</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge sut-eps-alert-bg text-dark">⚠️</span></td>
                                        <td class="p-1">Alerta</td>
                                        <td class="p-1 text-center">50-85%</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge sut-eps-high-bg text-dark">🔶</span></td>
                                        <td class="p-1">Alto</td>
                                        <td class="p-1 text-center">85-100%</td>
                                    </tr>
                                    <tr>
                                        <td class="p-1 text-center"><span class="badge sut-eps-critical-bg text-dark">🚨</span></td>
                                        <td class="p-1">Crítico</td>
                                        <td class="p-1 text-center">&gt;100%</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Coluna 4: Limites Banco Capacitor -->
                <div class="col-lg-3 col-md-6">
                    ${capacitorLimitsTableHtml ? `
                    <div class="mb-2">
                        ${capacitorLimitsTableHtml}
                    </div>` : ''}
                </div>
            </div>

            <!-- Segunda linha: Bancos de Capacitores e Ícones -->
            <div class="row g-2 mt-1">
                <!-- Bancos de Capacitores -->
                <div class="col-lg-6 col-md-6">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered mb-0" style="font-size: 0.7rem;">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center p-1" colspan="3">Bancos de Capacitores</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="p-1 text-center"><span class="badge bg-success" style="font-size: 0.6rem;">V≤</span></td>
                                    <td class="p-1"><strong>Vteste ≤ Vbanco</strong></td>
                                    <td class="p-1 text-center">-</td>
                                </tr>
                                <tr>
                                    <td class="p-1 text-center"><span class="badge bg-warning" style="font-size: 0.6rem;">V></span></td>
                                    <td class="p-1"><strong>Vteste > Vbanco</strong></td>
                                    <td class="p-1 text-center">fator 1.1x</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Ícones dos Bancos -->
                <div class="col-lg-6 col-md-6">
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered mb-0" style="font-size: 0.7rem;">
                            <thead class="table-light">
                                <tr>
                                    <th class="text-center p-1" colspan="3">Ícones dos Bancos</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="p-1 text-center" style="font-size: 0.9rem;">🔌</td>
                                    <td class="p-1"><strong>CS</strong></td>
                                    <td class="p-1">Chaves CS</td>
                                </tr>
                                <tr>
                                    <td class="p-1 text-center" style="font-size: 0.9rem;">⚡</td>
                                    <td class="p-1"><strong>Q</strong></td>
                                    <td class="p-1">Chaves Q</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>`;
}

function displayLoadResults(condicoesNominais, cenariosDetalhadosPorTapParam, limitsInfoParam) {
    // Store results in cache FIRST
    cachedLoadResults = {
        condicoesNominais: condicoesNominais,
        cenariosDetalhadosPorTap: cenariosDetalhadosPorTapParam,
        limitsInfo: limitsInfoParam
    };

    const nominaisContainer = document.getElementById('condicoes-nominais-card-body');
    if (nominaisContainer && condicoesNominais) { 
        nominaisContainer.innerHTML = `<div class="table-responsive"><table class="table table-sm table-striped table-hover small caption-top"><caption class="small text-muted text-center table-caption-top">Resumo Condições por Tap</caption><thead><tr class="table-light"><th>Parâmetro</th><th class="text-end">Tap Nominal</th><th class="text-end">Tap Menor</th><th class="text-end">Tap Maior</th></tr></thead><tbody><tr><td>Temperatura de Referência (°C)</td><td class="text-end">${formatVal(condicoesNominais.temperatura_referencia, 0)}</td><td class="text-end">${formatVal(condicoesNominais.temperatura_referencia, 0)}</td><td class="text-end">${formatVal(condicoesNominais.temperatura_referencia, 0)}</td></tr><tr><td>Tensão (kV)</td><td class="text-end">${formatVal(condicoesNominais.tensao_at_kv_nominal, 2)}</td><td class="text-end">${formatVal(condicoesNominais.tensao_at_kv_menor, 2)}</td><td class="text-end">${formatVal(condicoesNominais.tensao_at_kv_maior, 2)}</td></tr><tr><td>Corrente (A)</td><td class="text-end">${formatVal(condicoesNominais.corrente_at_a_nominal, 2)}</td><td class="text-end">${formatVal(condicoesNominais.corrente_at_a_menor, 2)}</td><td class="text-end">${formatVal(condicoesNominais.corrente_at_a_maior, 2)}</td></tr><tr><td>Vcc (%)</td><td class="text-end">${formatVal(condicoesNominais.vcc_percent_nominal, 2)}</td><td class="text-end">${formatVal(condicoesNominais.vcc_percent_menor, 2)}</td><td class="text-end">${formatVal(condicoesNominais.vcc_percent_maior, 2)}</td></tr><tr><td>Vcc (kV)</td><td class="text-end">${formatVal(condicoesNominais.vcc_kv_nominal, 2)}</td><td class="text-end">${formatVal(condicoesNominais.vcc_kv_menor, 2)}</td><td class="text-end">${formatVal(condicoesNominais.vcc_kv_maior, 2)}</td></tr><tr><td>Perdas em Carga Totais (${condicoesNominais.temperatura_referencia}°C) (kW)</td><td class="text-end">${formatVal(condicoesNominais.perdas_totais_kw_nominal_tref, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_totais_kw_menor_tref, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_totais_kw_maior_tref, 2)}</td></tr><tr><td>Perdas em Carga - Perdas em Vazio (${condicoesNominais.temperatura_referencia}°C) (kW)</td><td class="text-end">${formatVal(condicoesNominais.perdas_carga_s_vazio_kw_nominal_tref, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_carga_s_vazio_kw_menor_tref, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_carga_s_vazio_kw_maior_tref, 2)}</td></tr><tr><td>Perdas em Carga a (25°C) (kW)</td><td class="text-end">${formatVal(condicoesNominais.perdas_frio_25c_kw_nominal, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_frio_25c_kw_menor, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_frio_25c_kw_maior, 2)}</td></tr><tr><td>Perdas em Vazio (kW)</td><td class="text-end">${formatVal(condicoesNominais.perdas_vazio_kw_usada_calculo, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_vazio_kw_usada_calculo, 2)}</td><td class="text-end">${formatVal(condicoesNominais.perdas_vazio_kw_usada_calculo, 2)}</td></tr></tbody></table></div>`;
    } 
    else if (nominaisContainer) { nominaisContainer.innerHTML = '<div class="text-muted text-center small p-3">Erro ao carregar condições nominais.</div>'; }

    const detalhadosContainer = document.getElementById('resultados-perdas-carga');
    if (detalhadosContainer) {
        let htmlContent = '';
        if (!cenariosDetalhadosPorTapParam || !Array.isArray(cenariosDetalhadosPorTapParam) || cenariosDetalhadosPorTapParam.length === 0) { 
            htmlContent = `<div class="alert alert-warning text-center small p-2"><i class="fas fa-exclamation-triangle me-2"></i>Nenhum cenário detalhado encontrado.</div>`; 
            detalhadosContainer.innerHTML = htmlContent; 
            updateStatusGeralDynamically(); // Still update general status if table is empty
            return; 
        }
        const scenTestOrder = ["25°C", "Frio Inicio de Elevacao de Temperatura", "Quente", "1.2 pu", "1.4 pu"];
        
        scenTestOrder.forEach((scenTestName) => {
            const hasThisScenario = cenariosDetalhadosPorTapParam.some(tap => tap.cenarios_do_tap.some(scen => scen.nome_cenario_teste === scenTestName));
            if (!hasThisScenario) return;

            // Use .replace(/\s+/g, '-') to create a valid ID from the scenario name
            const scenTestNameId = scenTestName.replace(/\s+/g, '-');

            htmlContent += ` <div class="row g-2 mb-3 align-items-stretch"> <div class="col-12"> <div class="card card-sm h-100"> <div class="card-header card-header-sm text-center fw-bold card-header-dark">PERDAS EM CARGA ${scenTestName.toUpperCase()}</div> <div class="card-body p-1"> <h5 class="text-center my-2 fw-bold text-primary">📋 RESULTADOS DE TESTE E CONFIGURAÇÃO DO BANCO DE CAPACITORES</h5> <div class="table-responsive mb-3"> <table class="table table-sm table-bordered table-hover small caption-top losses-main-table"> <caption class="small text-muted text-center table-caption-top">Parâmetros de Teste e Configuração do Banco de Capacitores</caption> <thead> <tr class="table-light"> <th class="col-tap-dut">Tap DUT</th> <th class="col-vtest">Vtest (kV)</th> <th class="col-itest">Itest (A)</th> <th class="col-icap">Icap V≤ (A)</th> <th class="col-icap">Icap V> (A)</th> <th class="col-pativa">Patv (kW)</th> <th class="col-pteste">Ptest (MVA)</th> <th class="col-qef">Qef V≤ (MVAr)</th> <th class="col-qef">Qef V> (MVAr)</th> <th class="col-banco">Banco V≤</th> <th class="col-banco">Banco V></th> <th class="col-status">Status V≤</th> <th class="col-status">Status V></th> </tr> </thead><tbody>`;
            cenariosDetalhadosPorTapParam.forEach((tapData, tapIndexGlobal) => { 
                const scenIndexOriginal = tapData.cenarios_do_tap.findIndex(s => s.nome_cenario_teste === scenTestName);
                if (scenIndexOriginal === -1) return; 

                const cenario = tapData.cenarios_do_tap[scenIndexOriginal];
                const tp = cenario.test_params_cenario || {}; 
                const sf_bank_data = cenario.cap_bank_sf || { available_configurations: [], tensao_disp_kv: null }; 
                const cf_bank_data = cenario.cap_bank_cf || { available_configurations: [], tensao_disp_kv: null }; 
                
                const qEfetiva_sf_initial = cenario.q_efetiva_banco_sf_mvar;
                const iCapacitiva_sf_initial = cenario.i_capacitiva_sf_a;
                const qEfetiva_cf_initial = cenario.q_efetiva_banco_cf_mvar;
                const iCapacitiva_cf_initial = cenario.i_capacitiva_cf_a;

                const tapVoltage = getTapVoltageFromData(tapData.nome_tap); 
                const tapDisplayName = tapVoltage ? `${tapData.nome_tap} (${formatVal(tapVoltage, 1)}kV)` : tapData.nome_tap;
                
                const qEfetivaSfExceedsPteste = isQEfetivaExceedingPteste(qEfetiva_sf_initial, tp.pteste_mva);
                const qEfetivaCfExceedsPteste = isQEfetivaExceedingPteste(qEfetiva_cf_initial, tp.pteste_mva);
                const factorOvervoltage = parseFloat(document.getElementById('factor-cap-banc-overvoltage')?.value) || 1.1;
                const sfBankVoltage = cenario.cap_bank_sf?.tensao_disp_kv;
                const cfBankVoltage = cenario.cap_bank_cf?.tensao_disp_kv;

                // VERIFICAÇÕES DE VTEST E PATV PARA CORES NA TABELA
                const vtestExceedsSfLimit = isVtestExceedingBankLimit(tp.tensao_kv, sfBankVoltage, factorOvervoltage);
                const vtestExceedsCfLimit = isVtestExceedingBankLimit(tp.tensao_kv, cfBankVoltage, factorOvervoltage);
                const patvExceedsLimit = isPatvExceedingDutLimit(tp.pativa_kw);

                const qEfetivaSfClass = qEfetivaSfExceedsPteste ? 'q-efetiva-excede' : '';
                const qEfetivaCfClass = qEfetivaCfExceedsPteste ? 'q-efetiva-excede' : '';
                const vtestClass = (vtestExceedsSfLimit || vtestExceedsCfLimit) ? 'vtest-excede-limite' : '';
                const patvClass = patvExceedsLimit ? 'patv-excede-limite' : '';

                const qEfetivaSfTitle = qEfetivaSfExceedsPteste ? `title="Q_efetiva_banco V≤ (${formatVal(qEfetiva_sf_initial,3)} MVAr) excede P_teste (${formatVal(tp.pteste_mva, 3)} MVAr)" data-bs-toggle="tooltip"` : '';
                const qEfetivaCfTitle = qEfetivaCfExceedsPteste ? `title="Q_efetiva_banco V> (${formatVal(qEfetiva_cf_initial,3)} MVAr) excede P_teste (${formatVal(tp.pteste_mva, 3)} MVAr)" data-bs-toggle="tooltip"` : '';
                const vtestTitle = (vtestExceedsSfLimit || vtestExceedsCfLimit) ? `title="Vtest (${formatVal(tp.tensao_kv,2)} kV) excede limite do banco (${formatVal((sfBankVoltage || cfBankVoltage) * factorOvervoltage, 2)} kV)" data-bs-toggle="tooltip"` : '';
                const patvTitle = patvExceedsLimit ? `title="Potência ativa (${formatVal(tp.pativa_kw,1)} kW) excede limite DUT (1350 kW)" data-bs-toggle="tooltip"` : '';

                const qEfetivaSfValue = `<span class="${qEfetivaSfClass}" ${qEfetivaSfTitle}>${formatVal(qEfetiva_sf_initial, 3)}</span>`;
                const qEfetivaCfValue = `<span class="${qEfetivaCfClass}" ${qEfetivaCfTitle}>${formatVal(qEfetiva_cf_initial, 3)}</span>`;
                const vtestValue = `<span class="${vtestClass}" ${vtestTitle}>${formatVal(tp.tensao_kv, 2)}</span>`;
                const patvValue = `<span class="${patvClass}" ${patvTitle}>${formatVal(tp.pativa_kw, 1)}</span>`;
                
                // Get initial statuses from Python results
                const statusVMenor = cenario.status_v_menor || "OK";
                const statusVMaior = cenario.status_v_maior || "N/A";
                const statusVMenorWithIcon = getStatusWithIcon(statusVMenor);
                const statusVMaiorWithIcon = getStatusWithIcon(statusVMaior);
                const statusVMenorClass = getStatusClass(statusVMenor);
                const statusVMaiorClass = getStatusClass(statusVMaior);

                htmlContent += `<tr> <td class="text-center col-tap-dut">${tapDisplayName}</td> <td class="text-center col-vtest">${vtestValue}</td> <td class="text-center col-itest">${formatCurrentWithCtValidation(tp.corrente_a)}</td> <td class="text-center col-icap" id="icap-sf-${tapIndexGlobal}-${scenIndexOriginal}">${formatVal(iCapacitiva_sf_initial, 2)}</td> <td class="text-center col-icap" id="icap-cf-${tapIndexGlobal}-${scenIndexOriginal}">${formatVal(iCapacitiva_cf_initial, 2)}</td> <td class="text-center col-pativa">${patvValue}</td> <td class="text-center col-pteste">${formatVal(tp.pteste_mva, 3)}</td> <td class="text-center col-qef" id="qef-sf-${tapIndexGlobal}-${scenIndexOriginal}">${qEfetivaSfValue}</td> <td class="text-center col-qef" id="qef-cf-${tapIndexGlobal}-${scenIndexOriginal}">${qEfetivaCfValue}</td> <td class="col-banco">${renderBankOptionsHTML('sf', sf_bank_data.available_configurations, tapIndexGlobal, scenIndexOriginal, sf_bank_data.tensao_disp_kv)}</td> <td class="col-banco">${renderBankOptionsHTML('cf', cf_bank_data.available_configurations, tapIndexGlobal, scenIndexOriginal, cf_bank_data.tensao_disp_kv)}</td> <td class="col-status ${statusVMenorClass}">${statusVMenorWithIcon}</td> <td class="col-status ${statusVMaiorClass}">${statusVMaiorWithIcon}</td> </tr>`;
            });
            htmlContent += `</tbody></table></div>`;
            htmlContent += ` <hr class="my-2"> <h5 class="text-center mb-2 fw-bold text-primary">📊 ANÁLISE SUT/EPS (Compensada)</h5> <div class="text-center mb-1"> <small class="text-muted">Limite Corrente EPS: <strong>${formatVal(limitsInfoParam?.eps_current_limit_negative_a,0)}A</strong> a <strong>${formatVal(limitsInfoParam?.eps_current_limit_positive_a,0)}A</strong></small> </div> <div class="table-responsive" id="sut-eps-table-container-${scenTestNameId}"> ${generateHorizontalSutEpsTable(cenariosDetalhadosPorTapParam, scenTestName, limitsInfoParam)} </div> </div> </div> </div> </div>`; 
        });
        detalhadosContainer.innerHTML = htmlContent;
        initializeBankTooltips();
        // After rendering the table with default selections, update the general status card
        updateStatusGeralDynamically();
        // Update the legend with dynamic limits
        updateLoadLossesLegend(limitsInfoParam);
    } else {
        console.warn('[losses] Container de resultados detalhados "resultados-perdas-carga" not found.');
        updateStatusGeralDynamically(); // Still update general status if table container is missing
    }
}




function getTapVoltageFromData(tapName) { if (!cachedBasicData) { return null; } let voltage = null; switch (tapName) { case 'Nominal': voltage = cachedBasicData.tensao_at; break; case 'Menor': voltage = cachedBasicData.tensao_at_tap_menor; break; case 'Maior': voltage = cachedBasicData.tensao_at_tap_maior; break; default: return null; } return voltage ? parseFloat(voltage) : null; }
function initializeBankTooltips() { const existingTooltips = document.querySelectorAll('.tooltip'); existingTooltips.forEach(tooltip => { const instance = bootstrap.Tooltip.getInstance(tooltip); if (instance) { instance.dispose(); } }); const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]'); tooltipElements.forEach(element => { if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) { new bootstrap.Tooltip(element, { trigger: 'hover', placement: 'top', delay: { show: 300, hide: 100 } }); } }); }
function updateCapacitorBankLegend() { const factorInput = document.getElementById('factor-cap-banc-overvoltage'); const legendSpan = document.querySelector('.capacitor-bank-legend-factor'); if (factorInput && legendSpan) { const currentFactor = parseFloat(factorInput.value) || 1.1; legendSpan.textContent = `${currentFactor}x`; } }
function setupCapacitorBankLegendUpdater() { const factorInput = document.getElementById('factor-cap-banc-overvoltage'); if (factorInput) { factorInput.addEventListener('input', updateCapacitorBankLegend); factorInput.addEventListener('change', updateCapacitorBankLegend); updateCapacitorBankLegend(); } }

function generateHorizontalSutEpsTable(cenariosDetalhadosPorTapGlobal, scenTestName, limitsInfo) {
    if (!cachedBasicData) {
        return '<div class="text-center text-muted p-3 small">Dados básicos não disponíveis para tabela SUT/EPS.</div>';
    }
    if (!limitsInfo || typeof limitsInfo.eps_current_limit_positive_a === 'undefined' || typeof limitsInfo.eps_current_limit_negative_a === 'undefined') {
        return '<div class="text-center text-muted p-3 small">Informações de limites EPS ausentes.</div>';
    }

    const dutTapsWithDataForScenario = cenariosDetalhadosPorTapGlobal.filter(tapData => {
        const cenario = tapData.cenarios_do_tap.find(s => s.nome_cenario_teste === scenTestName);
        return cenario?.sut_eps_analysis?.some(sutAnal => sutAnal.sut_tap_kv !== undefined);
    });
    
    if (dutTapsWithDataForScenario.length === 0) {
        return '<div class="text-center text-muted p-3 small">Nenhum dado SUT/EPS disponível para os Taps DUT neste cenário.</div>';
    }
    
    const relevantSutTapsPerDutTap = {}; 
    let allRelevantSutTapsUnion = new Set(); 

    dutTapsWithDataForScenario.forEach((dutTapData, tapIndexGlobal) => {
        const cenario = dutTapData.cenarios_do_tap.find(s => s.nome_cenario_teste === scenTestName);
        const vTestKvForThisDutTap = cenario?.test_params_cenario?.tensao_kv;
        
        let localSutTapsForThisDutTap = []; 
        if (cenario && cenario.sut_eps_analysis && Array.isArray(cenario.sut_eps_analysis)) {
            localSutTapsForThisDutTap = Array.from(new Set(cenario.sut_eps_analysis.map(s => s.sut_tap_kv).filter(kv => kv !== undefined && typeof kv === 'number'))).sort((a,b) => a - b);
        }

        if (typeof vTestKvForThisDutTap === 'number' && vTestKvForThisDutTap > 0 && localSutTapsForThisDutTap.length > 0) {
            const targetSutRefV = vTestKvForThisDutTap * 1000.0;
            let idealSutTapKv = null;
            const tapsGteVtest = localSutTapsForThisDutTap.filter(tapKv => (tapKv * 1000) >= targetSutRefV - 1e-6);

            if (tapsGteVtest.length > 0) {
                idealSutTapKv = Math.min(...tapsGteVtest);
            } else { 
                idealSutTapKv = Math.max(...localSutTapsForThisDutTap);
            }

            const idealIndex = localSutTapsForThisDutTap.indexOf(idealSutTapKv);
            
            let selectedTaps = []; 
            if (idealIndex !== -1) { 
                const endIndex = Math.min(idealIndex + 4, localSutTapsForThisDutTap.length - 1);
                selectedTaps = localSutTapsForThisDutTap.slice(idealIndex, endIndex + 1);
                
                if (selectedTaps.length < 5 && idealIndex > 0) {
                    const neededFromBelow = 5 - selectedTaps.length;
                    const availableBelow = idealIndex;
                    const actualTakeFromBelow = Math.min(neededFromBelow, availableBelow);
                    const startBelowIndex = idealIndex - actualTakeFromBelow;
                    const tapsFromBelow = localSutTapsForThisDutTap.slice(startBelowIndex, idealIndex);
                    selectedTaps = [...tapsFromBelow, ...selectedTaps];
                }
            } else if (localSutTapsForThisDutTap.length > 0) { 
                 selectedTaps = localSutTapsForThisDutTap.slice(0, Math.min(5, localSutTapsForThisDutTap.length));
            }

            relevantSutTapsPerDutTap[dutTapData.nome_tap] = new Set(selectedTaps);
            selectedTaps.forEach(tap => allRelevantSutTapsUnion.add(tap));
        } else {
            relevantSutTapsPerDutTap[dutTapData.nome_tap] = new Set(); 
        }
    });

    const sortedSutTapsForTableRows = Array.from(allRelevantSutTapsUnion).sort((a, b) => parseFloat(a) - parseFloat(b));

    if (sortedSutTapsForTableRows.length === 0) {
        return '<div class="text-center text-muted small p-3">Nenhum Tap SUT relevante encontrado para exibição neste cenário (após filtragem).</div>';
    }

    const epsLimitPosA = limitsInfo.eps_current_limit_positive_a;
    const epsLimitNegA = limitsInfo.eps_current_limit_negative_a;
    
    let tableHtml = `<table class="table table-sm table-bordered table-hover sut-eps-horizontal-table small"><thead>`;
    
    tableHtml += `<tr class="sut-eps-header-row">`;
    dutTapsWithDataForScenario.forEach(tapData => {
        const tapVoltage = getTapVoltageFromData(tapData.nome_tap);
        const tapDisplayName = tapVoltage ? `${tapData.nome_tap}<br>(${formatVal(tapVoltage, 1)}kV)` : tapData.nome_tap;
        tableHtml += `<th colspan="3" class="text-center sut-eps-header-cell">${tapDisplayName}</th>`;
    });
    tableHtml += `</tr>`;

    tableHtml += `<tr class="sut-eps-header-row">`;
    dutTapsWithDataForScenario.forEach(() => {
        tableHtml += `<th class="text-center sut-eps-subheader-cell col-sut-tap-in-group">Tap SUT<br>(kV)</th>
                      <th class="text-center sut-eps-subheader-cell col-ieps-in-group">Ieps V≤<br>(A)</th>
                      <th class="text-center sut-eps-subheader-cell col-ieps-in-group">Ieps V><br>(A)</th>`;
    });
    tableHtml += `</tr></thead><tbody>`;

    const filteredSutTapsForTableRows = sortedSutTapsForTableRows.filter(sutTapKv_ForRow => {
        return dutTapsWithDataForScenario.some(dutTapData => {
            const cenarioForThisDutTap = dutTapData.cenarios_do_tap.find(s => s.nome_cenario_teste === scenTestName);
            const relevantSutSetForThisDutTap = relevantSutTapsPerDutTap[dutTapData.nome_tap];
            return cenarioForThisDutTap &&
                   cenarioForThisDutTap.test_params_cenario &&
                   relevantSutSetForThisDutTap &&
                   relevantSutSetForThisDutTap.has(sutTapKv_ForRow);
        });
    });

    filteredSutTapsForTableRows.forEach(sutTapKv_ForRow => {
        tableHtml += `<tr>`;
        dutTapsWithDataForScenario.forEach((dutTapData, dutTapColumnIndex) => {
            const originalTapIndexGlobal = cenariosDetalhadosPorTapGlobal.findIndex(gTap => gTap.nome_tap === dutTapData.nome_tap);
            const cenarioForThisDutTap = dutTapData.cenarios_do_tap.find(s => s.nome_cenario_teste === scenTestName);
            const originalScenIndex = cenarioForThisDutTap ? dutTapData.cenarios_do_tap.indexOf(cenarioForThisDutTap) : -1;

            const vTestKvForThisDutTap = cenarioForThisDutTap?.test_params_cenario?.tensao_kv;
            const relevantSutSetForThisDutTap = relevantSutTapsPerDutTap[dutTapData.nome_tap];

            if (!cenarioForThisDutTap || !cenarioForThisDutTap.test_params_cenario || !relevantSutSetForThisDutTap || !relevantSutSetForThisDutTap.has(sutTapKv_ForRow) || originalScenIndex === -1) {
                tableHtml += `<td class="text-center text-muted small col-sut-tap-in-group">-</td>
                              <td class="text-center text-muted small col-ieps-in-group">-</td>
                              <td class="text-center text-muted small col-ieps-in-group">-</td>`;
                return;
            }
            
            const sfConfigsList = cenarioForThisDutTap?.cap_bank_sf?.available_configurations || [];
            const cfConfigsList = cenarioForThisDutTap?.cap_bank_cf?.available_configurations || [];

            const sfRadioCurrentCol = document.querySelector(`input[name="bank-option-sf-${originalTapIndexGlobal}-${originalScenIndex}"]:checked`);
            const cfRadioCurrentCol = document.querySelector(`input[name="bank-option-cf-${originalTapIndexGlobal}-${originalScenIndex}"]:checked`);

            let sfConfigIndexCurrentCol = -1;
            let cfConfigIndexCurrentCol = -1;

            if (sfRadioCurrentCol) {
                sfConfigIndexCurrentCol = parseInt(sfRadioCurrentCol.dataset.configIndex);
            } else {
                const defaultSfConfig = sfConfigsList.find(config => config.is_initial_display_default === true);
                if (defaultSfConfig) sfConfigIndexCurrentCol = sfConfigsList.indexOf(defaultSfConfig);
                else if (sfConfigsList.length > 0) sfConfigIndexCurrentCol = 0; // Fallback to first if no default
            }

            if (cfRadioCurrentCol) {
                cfConfigIndexCurrentCol = parseInt(cfRadioCurrentCol.dataset.configIndex);
            } else {
                const defaultCfConfig = cfConfigsList.find(config => config.is_initial_display_default === true);
                if (defaultCfConfig) cfConfigIndexCurrentCol = cfConfigsList.indexOf(defaultCfConfig);
                 else if (cfConfigsList.length > 0) cfConfigIndexCurrentCol = 0; // Fallback to first if no default
            }

            const fallbackConfigSUT = { q_efetiva_banco_mvar: 0.0, q_config: "N/A (Interno SUT)" };
            const selectedSfConfig = (sfConfigIndexCurrentCol !== -1 && sfConfigsList.length > sfConfigIndexCurrentCol) ? sfConfigsList[sfConfigIndexCurrentCol] : fallbackConfigSUT;
            const selectedCfConfig = (cfConfigIndexCurrentCol !== -1 && cfConfigsList.length > cfConfigIndexCurrentCol) ? cfConfigsList[cfConfigIndexCurrentCol] : fallbackConfigSUT;

            const iTestA = cenarioForThisDutTap.test_params_cenario.corrente_a;
            const SUT_BT_VOLTAGE = 480; 
            const sqrt3_factor = (cachedBasicData.tipo_transformador.toLowerCase() === 'trifásico') ? Math.sqrt(3) : 1.0;
            const vSutTapV_calc = sutTapKv_ForRow * 1000;
            const ratioSut = (SUT_BT_VOLTAGE > 1e-6) ? vSutTapV_calc / SUT_BT_VOLTAGE : 0;
            
            const qEffSfRecalc = selectedSfConfig.q_efetiva_banco_mvar;
            const qEffCfRecalc = selectedCfConfig.q_efetiva_banco_mvar;

            const iCapSfRecalc = (vTestKvForThisDutTap * sqrt3_factor > 1e-6 && qEffSfRecalc > 1e-6) ? (qEffSfRecalc * 1000) / (vTestKvForThisDutTap * sqrt3_factor) : 0;
            let sfCurrent = (iTestA - iCapSfRecalc) * ratioSut;
            // Removido fator √3 para monofásico conforme solicitado
            // if (cachedBasicData.tipo_transformador.toLowerCase() === 'monofásico') {
            //     sfCurrent *= Math.sqrt(3);
            // }
            
            let cfCurrent = null;
            const cfBankIsEffectivelyAvailableForCell = cfConfigsList.length > 0 && !(cfConfigsList.length === 1 && cfConfigsList[0].q_config && cfConfigsList[0].q_config.startsWith("N/A"));
            if (cfBankIsEffectivelyAvailableForCell) {
                const iCapCfRecalc = (vTestKvForThisDutTap * sqrt3_factor > 1e-6 && qEffCfRecalc > 1e-6) ? (qEffCfRecalc * 1000) / (vTestKvForThisDutTap * sqrt3_factor) : 0;
                cfCurrent = (iTestA - iCapCfRecalc) * ratioSut;
                 // Removido fator √3 para monofásico conforme solicitado
                 // if (cachedBasicData.tipo_transformador.toLowerCase() === 'monofásico') {
                 //     cfCurrent *= Math.sqrt(3);
                 // }
            }
            
            let isIdealTapForThisCellDisplay = false; 
            if (typeof vTestKvForThisDutTap === 'number' && vTestKvForThisDutTap > 0) {
                 const targetSutRefV_idealCheck = vTestKvForThisDutTap * 1000.0;
                 let idealSutTapKv_check = null;
                let localSutTapsForIdealCheck = [];
                // Use the original full list from cenario.sut_eps_analysis for ideal check
                if (cenarioForThisDutTap.sut_eps_analysis && Array.isArray(cenarioForThisDutTap.sut_eps_analysis)) {
                    localSutTapsForIdealCheck = Array.from(new Set(cenarioForThisDutTap.sut_eps_analysis.map(s => s.sut_tap_kv).filter(kv => kv !== undefined && typeof kv === 'number'))).sort((a,b) => a-b);
                }

                 const tapsGteVtest_check = localSutTapsForIdealCheck.filter(tapKv => (tapKv * 1000) >= targetSutRefV_idealCheck - 1e-6);

                 if (tapsGteVtest_check.length > 0) idealSutTapKv_check = Math.min(...tapsGteVtest_check);
                 else if (localSutTapsForIdealCheck.length > 0) idealSutTapKv_check = Math.max(...localSutTapsForIdealCheck);
                 
                 if (idealSutTapKv_check !== null && Math.abs(sutTapKv_ForRow - idealSutTapKv_check) < 0.001) {
                    isIdealTapForThisCellDisplay = true;
                 }
            }

            const tapDisplayValue = `${formatVal(sutTapKv_ForRow, 2)}${isIdealTapForThisCellDisplay ? ' ⭐' : ''}`;
            const idealTapTitle = isIdealTapForThisCellDisplay ? `title="Tap SUT ideal para Vtest DUT ${formatVal(vTestKvForThisDutTap, 2)}kV" data-bs-toggle="tooltip"` : '';
            const tapCellClass = `text-center col-sut-tap-in-group sut-tap-value-cell ${isIdealTapForThisCellDisplay ? 'sut-tap-ideal-highlight' : ''}`;
            tableHtml += `<td class="${tapCellClass}" ${idealTapTitle}>${tapDisplayValue}</td>`;
            
            const sfCellClass = getSutEpsClass(sfCurrent, epsLimitPosA, epsLimitNegA) + '-bg text-dark';
            const sfIcon = getStatusIcon(sfCurrent, epsLimitPosA, epsLimitNegA);
            const sfValue = `${formatVal(sfCurrent, 2)} ${sfIcon}`;
            tableHtml += `<td class="text-center fw-bold ${sfCellClass} col-ieps-in-group">${sfValue}</td>`;

            let cfValueToDisplay = "-";
            let cfCellClass = 'sut-eps-na-bg text-dark'; 
            if (cfCurrent !== null) {
                cfCellClass = getSutEpsClass(cfCurrent, epsLimitPosA, epsLimitNegA) + '-bg text-dark';
                const cfIcon = getStatusIcon(cfCurrent, epsLimitPosA, epsLimitNegA);
                cfValueToDisplay = `${formatVal(cfCurrent, 2)} ${cfIcon}`;
            }
            tableHtml += `<td class="text-center fw-bold ${cfCellClass} col-ieps-in-group">${cfValueToDisplay}</td>`;
        });
        tableHtml += `</tr>`;
    });
    tableHtml += `</tbody></table><div class="mt-2 small text-muted"><i class="fas fa-info-circle me-1"></i><strong>⭐ Tap Ideal:</strong> Menor tap SUT ≥ Vtest DUT (para o respectivo Tap DUT e cenário). São exibidos até 5 Taps SUT relevantes em torno do ideal para cada Tap DUT.</div>`;
    return tableHtml;
}


async function loadLossesDataAndPopulateForm() {
    try {
        await waitForApiSystem();
        if (!window.apiDataSystem) { console.warn('[losses] apiDataSystem not available.'); return; }
        const store = window.apiDataSystem.getStore('losses');
        const data = await store.getData();

        if (data?.formData) {
            const formEl = document.getElementById('lossesTabContent');
            if (formEl) {
                fillFormWithData(formEl, data.formData);
                if (data.resultsNoLoad) displayNoLoadResults(data.resultsNoLoad);
                if (data.resultsLoad?.condicoes_nominais && data.resultsLoad?.cenarios_detalhados_por_tap && data.resultsLoad?.limites_info) {
                    displayLoadResults(data.resultsLoad.condicoes_nominais, data.resultsLoad.cenarios_detalhados_por_tap, data.resultsLoad.limites_info);
                    // Restore selections AFTER table is rendered by displayLoadResults.
                    // Using setTimeout(..., 0) to push the execution to the end of the event loop,
                    // ensuring the DOM is updated before we try to find the radio buttons.
                    setTimeout(() => {
                        if (data.bankSelections) {
                            restoreBankSelections(data.bankSelections);
                        }
                        // After restoring, update the general status card to reflect the restored state.
                        updateStatusGeralDynamically();
                    }, 0);
                }
            }
        }
    } catch (e) { console.error('[losses] Error loading/populating losses data:', e); }
}

async function initLosses() { 
    organizeMainCardsLayout(); // <<< CHAMADA DA NOVA FUNÇÃO PARA ORGANIZAR O LAYOUT
    await updateBasicDataCache(); 
    const panelId = 'transformer-info-losses-page'; 
    if (document.getElementById(panelId)) { 
        await loadAndPopulateTransformerInfo(panelId); 
    }
    
    const formCont = document.getElementById('lossesTabContent'); 
    if (formCont && window.setupApiFormPersistence) { 
        try { 
            await setupApiFormPersistence(formCont, 'losses'); 
        } catch (e) { console.error('[losses] Error configuring API persistence:', e); } 
    } 
    await loadLossesDataAndPopulateForm(); 
    
    document.addEventListener('transformerDataUpdated', async () => { 
        await resetAllCalculationResults(); 
        await updateBasicDataCache(); 
        if (document.getElementById(panelId)) {
            await loadAndPopulateTransformerInfo(panelId); 
        }
    }); 
    document.addEventListener('shown.bs.tab', async (ev) => { 
        if (ev.target.id === 'perdas-vazio-tab-btn' || ev.target.id === 'perdas-carga-tab-btn') { 
            if (document.getElementById(panelId)) {
                await loadAndPopulateTransformerInfo(panelId); 
            }
        } 
    }); 
    setupCalculateButtons(); 
    setupCapacitorBankLegendUpdater(); 
}
async function updateBasicDataCache() { 
    try { 
        await waitForApiSystem(); 
        if (!window.apiDataSystem) {
            cachedBasicData = null; return;
        }
        const store = window.apiDataSystem.getStore('transformerInputs'); 
        if (store) { 
            const data = await store.getData(); 
            cachedBasicData = (data?.formData && Object.keys(data.formData).length > 0) ? data.formData : null;
        } else { cachedBasicData = null; } 
    } catch (e) { cachedBasicData = null; console.error('[losses] Error updating basic data cache:', e); } 
}
async function resetAllCalculationResults() { 
    try { 
        clearCalculationResults('perdas-vazio'); 
        clearCalculationResults('perdas-carga'); 
        await waitForApiSystem(); 
        if (!window.apiDataSystem) { cachedLoadResults = null; return; }
        const store = window.apiDataSystem.getStore('losses');
        const currentData = await store.getData() || {};
        const cleanedData = {
            formData: currentData.formData || {},
            bankSelections: currentData.bankSelections || {} // Preserve bank selections if they exist
        };
        await store.updateData(cleanedData);
        cachedLoadResults = null;
        const statusGeralContainer = document.getElementById('status-geral-card-body');
        if (statusGeralContainer) {
            statusGeralContainer.innerHTML = '<p class="text-muted">Aguardando cálculo...</p>';
        }
    } catch (e) { console.error('[losses] Error resetting results:', e); } 
}
function clearCalculationResults(type) { if (type === 'perdas-vazio') { ['parametros-gerais-card-body', 'dut-voltage-level-results-body', 'sut-analysis-results-area', 'legend-card-no-load-area', 'no-load-attention-items-container-wrapper'].forEach(id => { const el = document.getElementById(id); if (el) el.innerHTML = ''; }); } else if (type === 'perdas-carga') { ['condicoes-nominais-card-body', 'resultados-perdas-carga', 'status-geral-card-body', 'legenda-status-card-body'].forEach(id => { const el = document.getElementById(id); if (el) el.innerHTML = ''; }); } }
function setupCalculateButtons() { const btnVazio = document.getElementById('calcular-perdas-vazio'); if (btnVazio) btnVazio.addEventListener('click', handleCalculateNoLoad); const btnCarga = document.getElementById('calcular-perdas-carga'); if (btnCarga) btnCarga.addEventListener('click', handleCalculateLoad); }

async function getBasicTransformerData() { 
    if (cachedBasicData && Object.keys(cachedBasicData).length > 0) return cachedBasicData; 
    await updateBasicDataCache(); 
    if (cachedBasicData && Object.keys(cachedBasicData).length > 0) return cachedBasicData; 
    console.error('[losses] CRITICAL: Failed to get basic transformer data.'); 
    showCalculationError('geral', 'Erro crítico: Dados básicos do transformador não puderam ser carregados. Verifique as entradas na aba principal.'); 
    return null; 
}
function collectNoLoadInputs() { return { perdas_vazio_ui: parseFloat(document.getElementById('perdas-vazio-kw')?.value) || 0, peso_nucleo_ui: parseFloat(document.getElementById('peso-projeto-Ton')?.value) || 0, corrente_excitacao_ui: parseFloat(document.getElementById('corrente-excitacao')?.value) || 0, inducao_ui: parseFloat(document.getElementById('inducao-nucleo')?.value) || 0, corrente_exc_1_1_ui: document.getElementById('corrente-excitacao-1-1')?.value ? parseFloat(document.getElementById('corrente-excitacao-1-1').value) : null, corrente_exc_1_2_ui: document.getElementById('corrente-excitacao-1-2')?.value ? parseFloat(document.getElementById('corrente-excitacao-1-2').value) : null, steel_type: document.getElementById('tipo-aco')?.value || 'M4' }; }
function collectLoadInputs() { return { temperatura_referencia: parseInt(document.getElementById('temperatura-referencia')?.value) || 75, perdas_carga_kw_u_min: parseFloat(document.getElementById('perdas-carga-kw_U_min')?.value) || 0, perdas_carga_kw_u_nom: parseFloat(document.getElementById('perdas-carga-kw_U_nom')?.value) || 0, perdas_carga_kw_u_max: parseFloat(document.getElementById('perdas-carga-kw_U_max')?.value) || 0, factor_cap_banc_overvoltage: parseFloat(document.getElementById('factor-cap-banc-overvoltage')?.value) || 1.1 }; }

function collectBankSelections() {
    const selections = {};
    const selectedRadios = document.querySelectorAll('input[type="radio"][name*="bank-option-"]:checked');
    selectedRadios.forEach(radio => {
        const name = radio.name;
        const configIndex = parseInt(radio.dataset.configIndex);
        const match = name.match(/bank-option-(sf|cf)-(\d+)-(\d+)/);
        if (match) {
            const [, type, tapIndex, scenIndex] = match;
            const key = `${tapIndex}-${scenIndex}-${type}`;
            selections[key] = configIndex;
        }
    });
    return selections;
}

function restoreBankSelections(selections) {
    if (!selections || typeof selections !== 'object') return;
    if (!cachedLoadResults || !cachedBasicData) {
        console.warn("[losses] restoreBankSelections: Dados cacheados ausentes. Tentando novamente em 300ms.");
        setTimeout(() => restoreBankSelections(selections), 300);
        return;
    }

    const scenariosToUpdate = new Set();

    Object.entries(selections).forEach(([key, configIndex]) => {
        const [tapIndex, scenIndex, bankType] = key.split('-');
        const radioName = `bank-option-${bankType}-${tapIndex}-${scenIndex}`;
        const radioId = `bank-radio-${bankType}-${tapIndex}-${scenIndex}-${configIndex}`;

        // Uncheck all radios in the group first
        document.querySelectorAll(`input[name="${radioName}"]`).forEach(radio => radio.checked = false);

        // Check the correct radio
        const targetRadio = document.getElementById(radioId);
        if (targetRadio) {
            targetRadio.checked = true;
            // Add the scenario key to a set to avoid duplicate updates
            scenariosToUpdate.add(`${tapIndex}-${scenIndex}`);
        } else {
            console.warn(`[losses] restoreBankSelections: Radiobutton ${radioId} não encontrado.`);
        }
    });

    // Now, trigger the update logic for each unique scenario that was changed
    scenariosToUpdate.forEach(scenarioKey => {
        const [tapIndex, scenIndex] = scenarioKey.split('-');
        if (window.losses_module && typeof window.losses_module.handleBankOptionChange === 'function') {
            // This call will recalculate and update the UI for the specific row and related elements
            window.losses_module.handleBankOptionChange(null, parseInt(tapIndex), parseInt(scenIndex));
        }
    });
}


async function saveBankSelectionsToStore() {
    try {
        await waitForApiSystem();
        if (!window.apiDataSystem) return;
        const store = window.apiDataSystem.getStore('losses');
        const currentData = await store.getData() || {};
        const bankSelections = collectBankSelections();
        const updatedData = { ...currentData, bankSelections: bankSelections };
        await store.updateData(updatedData);
    } catch (e) { console.error('[losses] Error saving bank selections:', e); }
}

async function handleCalculateNoLoad() { 
    showCalculationLoading('perdas-vazio'); 
    try { 
        const basicData = await getBasicTransformerData(); 
        if (!basicData) throw new Error('Dados básicos do transformador não disponíveis.'); 
        const noLoadInputs = collectNoLoadInputs(); 
        if (noLoadInputs.perdas_vazio_ui <= 0 || noLoadInputs.peso_nucleo_ui <= 0 || noLoadInputs.corrente_excitacao_ui <= 0 || noLoadInputs.inducao_ui <= 0) throw new Error('Inputs obrigatórios para Perdas em Vazio (Perdas, Peso, Corrente Exc., Indução) devem ser > 0.'); 
        if ((noLoadInputs.corrente_exc_1_1_ui !== null && noLoadInputs.corrente_exc_1_1_ui <= 0) || (noLoadInputs.corrente_exc_1_2_ui !== null && noLoadInputs.corrente_exc_1_2_ui <= 0)) throw new Error('Correntes de excitação opcionais (1.1pu, 1.2pu) devem ser > 0 se fornecidas.'); 
        
        const requiredBasicKeys = ['frequencia', 'tensao_bt', 'corrente_nominal_bt', 'tipo_transformador', 'grupo_ligacao', 'potencia_mva'];
        console.log('[losses] DEBUG: Verificando dados básicos:', basicData);
        for (const k of requiredBasicKeys) {
            const val = basicData[k];
            console.log(`[losses] DEBUG: Campo '${k}' = ${val} (tipo: ${typeof val})`);
            // Adjusted check for tensao_terciario and corrente_nominal_terciario to allow them to be undefined or null, but > 0 if present
            const isOptionalTertiary = (k === 'tensao_terciario' || k === 'corrente_nominal_terciario');
            const isStringField = (k === 'tipo_transformador' || k === 'grupo_ligacao');
            const isNumericCheckNeeded = (!isStringField && !isOptionalTertiary);

            if (val === undefined || val === null || (isNumericCheckNeeded && (typeof val !== 'number' || isNaN(val) || val <= 0 )) ) {
                if (isOptionalTertiary && (val === undefined || val === null)) {
                    // This is fine for optional tertiary, skip error
                } else if (isOptionalTertiary && (typeof val !== 'number' || isNaN(val) || val <=0)) {
                    // Error if tertiary data is present but invalid
                     throw new Error(`Dado básico opcional '${k}' está presente mas é inválido ou não > 0.`);
                }
                else {
                    console.error(`[losses] ERRO: Campo '${k}' inválido. Valor: ${val}, Tipo: ${typeof val}`);
                    throw new Error(`Dado básico do transformador '${k}' está ausente, inválido ou não é > 0.`);
                }
            }
        }
        
        const tertiaryData = {}; 
        if (basicData.tensao_terciario && parseFloat(basicData.tensao_terciario) > 0) { tertiaryData.tensao_terciario_kv = parseFloat(basicData.tensao_terciario); } 
        if (basicData.corrente_nominal_terciario && parseFloat(basicData.corrente_nominal_terciario) > 0) { tertiaryData.corrente_nominal_terciario = parseFloat(basicData.corrente_nominal_terciario); } 
        
        const payload = {
            operation: 'no_load_losses',
            data: {
                ...noLoadInputs,
                frequencia: parseFloat(basicData.frequencia),
                tensao_bt_kv: parseFloat(basicData.tensao_bt),
                corrente_nominal_bt: parseFloat(basicData.corrente_nominal_bt),
                tipo_transformador: basicData.tipo_transformador,
                grupo_ligacao: basicData.grupo_ligacao || "",
                potencia_mva: parseFloat(basicData.potencia_mva),
                ...tertiaryData
            }
        };
        const resp = await fetch('/api/transformer/modules/losses/process', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) }); 
        if (!resp.ok) { const errD = await resp.json(); throw new Error(`API Error (No-Load ${resp.status}): ${errD.detail || resp.statusText}`); } 
        const res = await resp.json(); 
        displayNoLoadResults(res.results); 
        await waitForApiSystem(); 
        if (!window.apiDataSystem) { console.warn("apiDataSystem not available for storing no-load results."); return; }
        const store = window.apiDataSystem.getStore('losses');
        const currStore = await store.getData() || {};
        const newForm = collectFormData(document.getElementById('lossesTabContent'));
        const updatedStore = {
            resultsNoLoad: res.results,
            formData: { ...(currStore.formData || {}), ...newForm },
            bankSelections: currStore.bankSelections || {} 
        };
        if (currStore.resultsLoad) updatedStore.resultsLoad = currStore.resultsLoad;
        await store.updateData(updatedStore);
    } catch (e) { 
        console.error('[losses] Error calc no-load:', e); 
        showCalculationError('perdas-vazio', e.message); 
    } 
}
async function handleCalculateLoad() { 
    showCalculationLoading('perdas-carga');
    try { 
        const basicData = await getBasicTransformerData(); 
        if (!basicData) throw new Error('Dados básicos do transformador não disponíveis.'); 
        
        await waitForApiSystem(); 
        if (!window.apiDataSystem) throw new Error("Sistema de persistência não disponível.");
        
        const lossesStore = window.apiDataSystem.getStore('losses'); 
        const storedData = await lossesStore.getData() || {}; 
        let perdasVazioCalc; 

        if (storedData.resultsNoLoad?.parametros_gerais_comparativo?.perdas_vazio_kw_usada_calculo !== undefined) {
            perdasVazioCalc = parseFloat(storedData.resultsNoLoad.parametros_gerais_comparativo.perdas_vazio_kw_usada_calculo);
        } else if (storedData.formData?.['perdas-vazio-kw'] !== undefined) { 
            perdasVazioCalc = parseFloat(storedData.formData['perdas-vazio-kw']);
        } else if (basicData?.perdas_vazio_kw !== undefined) { 
            perdasVazioCalc = parseFloat(basicData.perdas_vazio_kw);
        }

        if (perdasVazioCalc === undefined || perdasVazioCalc === null || isNaN(perdasVazioCalc) || perdasVazioCalc <= 0) {
            throw new Error('Perdas em Vazio (kW) inválidas ou não calculadas/encontradas (valor > 0). Verifique aba "Perdas em Vazio" ou Dados Gerais.');
        }
        
        const loadInputs = collectLoadInputs(); 
        if (loadInputs.perdas_carga_kw_u_min <= 0 || loadInputs.perdas_carga_kw_u_nom <= 0 || loadInputs.perdas_carga_kw_u_max <= 0) {
            throw new Error('Perdas em Carga (kW) para Taps (Min, Nominal, Max) devem ser > 0.');
        }
        
        const reqKeysLoad = ['potencia_mva', 'impedancia', 'tensao_at', 'tensao_at_tap_maior', 'tensao_at_tap_menor', 'impedancia_tap_maior', 'impedancia_tap_menor', 'corrente_nominal_at', 'corrente_nominal_at_tap_maior', 'corrente_nominal_at_tap_menor', 'tipo_transformador']; 
        for (const k of reqKeysLoad) {
            const val = basicData[k];
            // Adjusted check for tensao_terciario and corrente_nominal_terciario to allow them to be undefined or null, but > 0 if present
            const isOptionalTertiaryLoad = (k === 'tensao_terciario' || k === 'corrente_nominal_terciario');
            const isNumericCheckNeededLoad = (k !== 'tipo_transformador' && !isOptionalTertiaryLoad); 
            
            if (val === undefined || val === null || (isNumericCheckNeededLoad && (typeof val !== 'number' || isNaN(val) || val <= 0)) ) {
                 if (isOptionalTertiaryLoad && (val === undefined || val === null)) {
                    // This is fine for optional tertiary in load context, skip error
                } else if (isOptionalTertiaryLoad && (typeof val !== 'number' || isNaN(val) || val <=0)) {
                     throw new Error(`Dado básico opcional '${k}' para carga está presente mas é inválido ou não > 0.`);
                } else {
                    throw new Error(`Dado básico '${k}' para cálculo de carga ausente, inválido ou não > 0. Verifique Dados Gerais.`);
                }
            }
        }
        
        const tertiaryDataLoad = {};
        if (basicData.tensao_terciario && parseFloat(basicData.tensao_terciario) > 0) { tertiaryDataLoad.tensao_terciario_kv = parseFloat(basicData.tensao_terciario); }
        if (basicData.corrente_nominal_terciario && parseFloat(basicData.corrente_nominal_terciario) > 0) { tertiaryDataLoad.corrente_nominal_terciario = parseFloat(basicData.corrente_nominal_terciario); }

        // Additional voltage data for conditional load loss scenarios
        const additionalVoltageData = {};
        if (basicData.tensao_bt && parseFloat(basicData.tensao_bt) > 0) { additionalVoltageData.tensao_bt_kv = parseFloat(basicData.tensao_bt); }
        if (basicData.classe_tensao_at && parseFloat(basicData.classe_tensao_at) > 0) { additionalVoltageData.classe_tensao_at = parseFloat(basicData.classe_tensao_at); }
        if (basicData.classe_tensao_bt && parseFloat(basicData.classe_tensao_bt) > 0) { additionalVoltageData.classe_tensao_bt = parseFloat(basicData.classe_tensao_bt); }
        if (basicData.classe_tensao_terciario && parseFloat(basicData.classe_tensao_terciario) > 0) { additionalVoltageData.classe_tensao_terciario = parseFloat(basicData.classe_tensao_terciario); }

        const payload = {
            operation: 'load_losses',
            data: {
                ...loadInputs,
                potencia_mva: parseFloat(basicData.potencia_mva),
                impedancia: parseFloat(basicData.impedancia),
                tensao_at_kv: parseFloat(basicData.tensao_at),
                tensao_at_tap_maior_kv: parseFloat(basicData.tensao_at_tap_maior),
                tensao_at_tap_menor_kv: parseFloat(basicData.tensao_at_tap_menor),
                impedancia_tap_maior: parseFloat(basicData.impedancia_tap_maior),
                impedancia_tap_menor: parseFloat(basicData.impedancia_tap_menor),
                corrente_nominal_at_a: parseFloat(basicData.corrente_nominal_at),
                corrente_nominal_at_tap_maior_a: parseFloat(basicData.corrente_nominal_at_tap_maior),
                corrente_nominal_at_tap_menor_a: parseFloat(basicData.corrente_nominal_at_tap_menor),
                tipo_transformador: basicData.tipo_transformador,
                grupo_ligacao: basicData.grupo_ligacao || "",
                perdas_vazio_kw_calculada: perdasVazioCalc,
                ...tertiaryDataLoad,
                ...additionalVoltageData
            }
        };
        const resp = await fetch('/api/transformer/modules/losses/process', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) }); 
        if (!resp.ok) { const errD = await resp.json(); throw new Error(`API Error (Load ${resp.status}): ${errD.detail || resp.statusText}`); } 
        const res = await resp.json(); 
        
        if (res.results?.condicoes_nominais && res.results?.cenarios_detalhados_por_tap && res.results?.limites_info) { 
            displayLoadResults(res.results.condicoes_nominais, res.results.cenarios_detalhados_por_tap, res.results.limites_info); 
        } else { 
            throw new Error('Estrutura de resultados de perdas em carga inválida do backend.'); 
        } 
        
        const store = window.apiDataSystem.getStore('losses');
        const currStore = await store.getData() || {};
        const newForm = collectFormData(document.getElementById('lossesTabContent'));
        const updatedStore = {
            resultsLoad: res.results,
            formData: { ...(currStore.formData || {}), ...newForm },
            // Reset bank selections on new calculation; defaults will be set by displayLoadResults
            // based on the `is_initial_display_default` from the backend.
            bankSelections: {} 
        };
        if (currStore.resultsNoLoad) updatedStore.resultsNoLoad = currStore.resultsNoLoad;
        await store.updateData(updatedStore);
    } catch (e) { 
        console.error('[losses] Error calc load:', e); 
        showCalculationError('perdas-carga', e.message); 
        cachedLoadResults = null;
    } 
}

function showCalculationLoading(type) { const loadHTML = `<div class="d-flex align-items-center justify-content-center p-3"><div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div><span class="text-muted small">Calculando...</span></div>`; let areas; if (type === 'perdas-vazio') { areas = ['parametros-gerais-card-body', 'dut-voltage-level-results-body', 'sut-analysis-results-area', 'legend-card-no-load-area', 'no-load-attention-items-container-wrapper']; } else { areas = ['condicoes-nominais-card-body', 'resultados-perdas-carga', 'status-geral-card-body', 'legenda-status-card-body']; } areas.forEach(id => { const el = document.getElementById(id); if (el) { el.innerHTML = loadHTML; el.classList.remove('results-placeholder-error'); } }); }
function showCalculationError(type, msg) { const errHTML = `<div class="alert alert-danger alert-sm mb-0 small p-2 font-size-07rem" role="alert"><i class="fas fa-exclamation-triangle me-1"></i> Erro: ${msg}</div>`; let areas; if (type === 'perdas-vazio') { areas = ['parametros-gerais-card-body', 'dut-voltage-level-results-body', 'sut-analysis-results-area', 'legend-card-no-load-area', 'no-load-attention-items-container-wrapper']; } else if (type === 'perdas-carga') { areas = ['condicoes-nominais-card-body', 'resultados-perdas-carga', 'status-geral-card-body', 'legenda-status-card-body']; } else { areas = []; const genErr = document.getElementById('transformer-info-losses-page'); if (genErr) { const errDiv = document.createElement('div'); errDiv.className = 'container-fluid mt-2'; errDiv.innerHTML = errHTML; const oldErr = genErr.querySelector('.alert-danger'); if (oldErr?.closest('.container-fluid.mt-2')) { oldErr.closest('.container-fluid.mt-2').remove(); } genErr.prepend(errDiv); } return; } areas.forEach(id => { const el = document.getElementById(id); if (el) { el.innerHTML = errHTML; el.classList.add('results-placeholder-error'); } }); }

document.addEventListener('moduleContentLoaded', (event) => { if (event.detail?.moduleName === 'losses') { if (document.getElementById('lossesTabContent')) { initLosses(); } } });
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.losses-tab-btn'); // Use a more specific class for these tab buttons
    tabButtons.forEach(button => {
        button.addEventListener('hide.bs.tab', async function(event) { // Use 'hide.bs.tab' to save *before* switching
            if (event.target.id === 'perdas-carga-tab-btn') { // Saving when leaving Load Losses tab
                await saveBankSelectionsToStore();
                // Also save form data if necessary
                if (window.apiDataSystem) {
                    const formEl = document.getElementById('lossesTabContent');
                    if (formEl && window.collectFormData) {
                        const formData = window.collectFormData(formEl);
                        const store = window.apiDataSystem.getStore('losses');
                        const currentData = await store.getData() || {};
                        const updatedData = {
                            ...currentData,
                            formData: { ...(currentData.formData || {}), ...formData }
                        };
                        await store.updateData(updatedData);
                    }
                }
            }
        });
        button.addEventListener('shown.bs.tab', async function(event) { // Load when entering a tab
             if (event.target.id === 'perdas-vazio-tab-btn' || event.target.id === 'perdas-carga-tab-btn') {
                await loadLossesDataAndPopulateForm(); // This handles restoring form and results
             }
        });
    });

    window.addEventListener('beforeunload', async function() {
        const activeTab = document.querySelector('#lossesTab .nav-link.active');
        if (activeTab && activeTab.id === 'perdas-carga-tab-btn') {
           await saveBankSelectionsToStore();
           // Consider saving full form data too
            if (window.apiDataSystem) {
                const formEl = document.getElementById('lossesTabContent');
                if (formEl && window.collectFormData) {
                    const formData = window.collectFormData(formEl);
                    const store = window.apiDataSystem.getStore('losses');
                    const currentData = await store.getData() || {};
                    const updatedData = {
                        ...currentData,
                        formData: { ...(currentData.formData || {}), ...formData }
                    };
                    await store.updateData(updatedData);
                }
            }
        }
    });
    document.addEventListener('sessionRestored', async function(event) {
        // Wait a bit for other modules to potentially load/restore their data if needed
        setTimeout(async () => {
            await loadLossesDataAndPopulateForm();
        }, 500);
    });
});